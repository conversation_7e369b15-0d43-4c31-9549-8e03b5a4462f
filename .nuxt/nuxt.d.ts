// Generated by nuxi
/// <reference types="@vueuse/nuxt" />
/// <reference types="nuxt-icon" />
/// <reference types="@nuxt/devtools" />
/// <reference types="@nuxt/telemetry" />
/// <reference types="@vite-pwa/nuxt" />
/// <reference path="types/builder-env.d.ts" />
/// <reference types="nuxt" />
/// <reference path="types/app-defaults.d.ts" />
/// <reference path="types/plugins.d.ts" />
/// <reference path="types/build.d.ts" />
/// <reference path="types/schema.d.ts" />
/// <reference path="types/app.config.d.ts" />
/// <reference path="../node_modules/@vite-pwa/nuxt/dist/runtime/plugins/types" />
/// <reference types="@vite-pwa/nuxt/configuration" />
/// <reference types="vite-plugin-pwa/vue" />
/// <reference types="vite-plugin-pwa/info" />
/// <reference types="vite-plugin-pwa/pwa-assets" />
/// <reference path="pwa-icons/pwa-icons.d.ts" />
/// <reference path="pwa-icons/PwaTransparentImage.d.ts" />
/// <reference path="pwa-icons/PwaMaskableImage.d.ts" />
/// <reference path="pwa-icons/PwaFaviconImage.d.ts" />
/// <reference path="pwa-icons/PwaAppleImage.d.ts" />
/// <reference path="pwa-icons/PwaAppleSplashScreenImage.d.ts" />
/// <reference types="vue-router" />
/// <reference path="types/middleware.d.ts" />
/// <reference path="types/layouts.d.ts" />
/// <reference path="components.d.ts" />
/// <reference path="imports.d.ts" />
/// <reference path="types/imports.d.ts" />
/// <reference path="schema/nuxt.schema.d.ts" />
/// <reference path="types/nitro.d.ts" />

export {}
