export { useScriptTriggerConsent, useScriptEventPage, useScriptTriggerElement, useScript, useScriptGoogleAnalytics, useScriptPlausibleAnalytics, useScriptClarity, useScriptCloudflareWebAnalytics, useScriptFathomAnalytics, useScriptMatomoAnalytics, useScriptGoogleTagManager, useScriptGoogleAdsense, useScriptSegment, useScriptMetaPixel, useScriptXPixel, useScriptIntercom, useScriptHotjar, useScriptStripe, useScriptLemonSqueezy, useScriptVimeoPlayer, useScriptYouTubePlayer, useScriptGoogleMaps, useScriptNpm, useScriptCrisp } from '#app/composables/script-stubs';
export { isVue2, isVue3 } from 'vue-demi';
export { defineNuxtLink } from '#app/components/nuxt-link';
export { useNuxtApp, tryUseNuxtApp, defineNuxtPlugin, definePayloadPlugin, useRuntimeConfig, defineAppConfig } from '#app/nuxt';
export { requestIdleCallback, cancelIdleCallback } from '#app/compat/idle-callback';
export { setInterval } from '#app/compat/interval';
export { useAppConfig, updateAppConfig } from '#app/config';
export { defineNuxtComponent } from '#app/composables/component';
export { useAsyncData, useLazyAsyncData, useNuxtData, refreshNuxtData, clearNuxtData } from '#app/composables/asyncData';
export { useHydration } from '#app/composables/hydrate';
export { callOnce } from '#app/composables/once';
export { useState, clearNuxtState } from '#app/composables/state';
export { clearError, createError, isNuxtError, showError, useError } from '#app/composables/error';
export { useFetch, useLazyFetch } from '#app/composables/fetch';
export { useCookie, refreshCookie } from '#app/composables/cookie';
export { onPrehydrate, prerenderRoutes, useRequestHeader, useRequestHeaders, useResponseHeader, useRequestEvent, useRequestFetch, setResponseStatus } from '#app/composables/ssr';
export { onNuxtReady } from '#app/composables/ready';
export { preloadComponents, prefetchComponents, preloadRouteComponents } from '#app/composables/preload';
export { abortNavigation, addRouteMiddleware, defineNuxtRouteMiddleware, setPageLayout, navigateTo, useRoute, useRouter } from '#app/composables/router';
export { isPrerendered, loadPayload, preloadPayload, definePayloadReducer, definePayloadReviver } from '#app/composables/payload';
export { useLoadingIndicator } from '#app/composables/loading-indicator';
export { getAppManifest, getRouteRules } from '#app/composables/manifest';
export { reloadNuxtApp } from '#app/composables/chunk';
export { useRequestURL } from '#app/composables/url';
export { usePreviewMode } from '#app/composables/preview';
export { useRouteAnnouncer } from '#app/composables/route-announcer';
export { useRuntimeHook } from '#app/composables/runtime-hook';
export { onBeforeRouteLeave, onBeforeRouteUpdate, useLink } from 'vue-router';
export { withCtx, withDirectives, withKeys, withMemo, withModifiers, withScopeId, onActivated, onBeforeMount, onBeforeUnmount, onBeforeUpdate, onDeactivated, onErrorCaptured, onMounted, onRenderTracked, onRenderTriggered, onServerPrefetch, onUnmounted, onUpdated, computed, customRef, isProxy, isReactive, isReadonly, isRef, markRaw, proxyRefs, reactive, readonly, ref, shallowReactive, shallowReadonly, shallowRef, toRaw, toRef, toRefs, triggerRef, unref, watch, watchEffect, watchPostEffect, watchSyncEffect, isShallow, effect, effectScope, getCurrentScope, onScopeDispose, defineComponent, defineAsyncComponent, resolveComponent, getCurrentInstance, h, inject, hasInjectionContext, nextTick, provide, mergeModels, toValue, useModel, useAttrs, useCssModule, useCssVars, useSlots, useTransitionState, useId, useTemplateRef, useShadowRoot, Component, ComponentPublicInstance, ComputedRef, DirectiveBinding, ExtractDefaultPropTypes, ExtractPropTypes, ExtractPublicPropTypes, InjectionKey, PropType, Ref, MaybeRef, MaybeRefOrGetter, VNode, WritableComputedRef } from 'vue';
export { computedAsync, asyncComputed, computedEager, eagerComputed, computedInject, computedWithControl, controlledComputed, createEventHook, createGlobalState, createInjectionState, createReusableTemplate, createSharedComposable, createTemplatePromise, createUnrefFn, extendRef, injectLocal, isDefined, makeDestructurable, onClickOutside, onKeyStroke, onLongPress, onStartTyping, provideLocal, reactify, createReactiveFn, reactifyObject, reactiveComputed, reactiveOmit, reactivePick, refAutoReset, autoResetRef, refDebounced, useDebounce, debouncedRef, refDefault, refThrottled, useThrottle, throttledRef, refWithControl, controlledRef, syncRef, syncRefs, templateRef, toReactive, resolveRef, resolveUnref, tryOnBeforeMount, tryOnBeforeUnmount, tryOnMounted, tryOnScopeDispose, tryOnUnmounted, unrefElement, until, useActiveElement, useAnimate, useArrayDifference, useArrayEvery, useArrayFilter, useArrayFind, useArrayFindIndex, useArrayFindLast, useArrayIncludes, useArrayJoin, useArrayMap, useArrayReduce, useArraySome, useArrayUnique, useAsyncQueue, useAsyncState, useBase64, useBattery, useBluetooth, useBreakpoints, useBroadcastChannel, useBrowserLocation, useCached, useClipboard, useClipboardItems, useCloned, useColorMode, useConfirmDialog, useCounter, useCssVar, useCurrentElement, useCycleList, useDark, useDateFormat, useDebouncedRefHistory, useDebounceFn, useDeviceMotion, useDeviceOrientation, useDevicePixelRatio, useDevicesList, useDisplayMedia, useDocumentVisibility, useDraggable, useDropZone, useElementBounding, useElementByPoint, useElementHover, useElementSize, useElementVisibility, useEventBus, useEventListener, useEventSource, useEyeDropper, useFavicon, useFileDialog, useFileSystemAccess, useFocus, useFocusWithin, useFps, useFullscreen, useGamepad, useGeolocation, useIdle, useInfiniteScroll, useIntersectionObserver, useInterval, useIntervalFn, useKeyModifier, useLastChanged, useLocalStorage, useMagicKeys, useManualRefHistory, useMediaControls, useMediaQuery, useMemoize, useMemory, useMounted, useMouse, useMouseInElement, useMousePressed, useMutationObserver, useNavigatorLanguage, useNetwork, useNow, useObjectUrl, useOffsetPagination, useOnline, usePageLeave, useParallax, useParentElement, usePerformanceObserver, usePermission, usePointer, usePointerLock, usePointerSwipe, usePreferredColorScheme, usePreferredContrast, usePreferredDark, usePreferredLanguages, usePreferredReducedMotion, usePrevious, useRafFn, useRefHistory, useResizeObserver, useScreenOrientation, useScreenSafeArea, useScriptTag, useScroll, useScrollLock, useSessionStorage, useShare, useSorted, useSpeechRecognition, useSpeechSynthesis, useStepper, useStorageAsync, useStyleTag, useSupported, useSwipe, useTemplateRefsList, useTextareaAutosize, useTextDirection, useTextSelection, useThrottledRefHistory, useThrottleFn, useTimeAgo, useTimeout, useTimeoutFn, useTimeoutPoll, useTimestamp, useToggle, useToNumber, useToString, useTransition, useUrlSearchParams, useUserMedia, useVibrate, useVirtualList, useVModel, useVModels, useWakeLock, useWebNotification, useWebSocket, useWebWorker, useWebWorkerFn, useWindowFocus, useWindowScroll, useWindowSize, watchArray, watchAtMost, watchDebounced, debouncedWatch, watchDeep, watchIgnorable, ignorableWatch, watchImmediate, watchOnce, watchPausable, pausableWatch, watchThrottled, throttledWatch, watchTriggerable, watchWithFilter, whenever } from '@vueuse/core';
export { injectHead, useHead, useSeoMeta, useHeadSafe, useServerHead, useServerSeoMeta, useServerHeadSafe } from '@unhead/vue';
export { useActivityLogger } from '../composables/activity-logger';
export { useActivityTracking } from '../composables/activity-tracking';
export { useAdminDashboard } from '../composables/admin-dashboard';
export { useAnalyticsTracking } from '../composables/analytics-tracking';
export { useAuthUtils } from '../composables/auth-utils';
export { useBlogs } from '../composables/blog';
export { useBuilderComponents } from '../composables/builder-components';
export { useContentModeration } from '../composables/content-moderation';
export { useContent } from '../composables/content';
export { useCRM } from '../composables/crm';
export { database } from '../composables/database';
export { sendEmail, createEmail } from '../composables/email';
export { incrementValue, loginUser, logoutUser, registerUser, queryByCollection, queryByCollectionLimit, queryByWhereLimit, queryByWhereDoubleLimit, queryByWhereTrippleLimit, queryByWhere, queryByWhere2, queryByWhereFar, queryByWhereDouble, queryById, deleteById, set, setById, update, add, setBatch, setBatchMore, updateBatch, deleteBatch, del, upload, updateUserRole, toggleUserStatus, addFcmToken, checkNotificationsEnabled } from '../composables/firebase';
export { schemaSkin, schemaHeaders } from '../composables/forms';
export { useGeolocationWithFallback } from '../composables/geolocation';
export { deleteItems, getMeta, logBatteryInfo, useBrowserUrl, hexToRGBA, makeDateTimeUserReadable, makeDateTimeReadable, extraInfo, extraInfoUpdate, removeExtra, formatPhoneNumber, formatPhoneNumberPlus, isAdmin, isSuperAdmin, hasRole, extraUpdateInfo, getSrc, randomPerson, randomBotUrl, removeUndefined, hexToRgbA, RGBToHex, hexToHSL, formatter, updateRoot, LightenDarkenColorHex, getRandomInt, makeid, randomItemInArray, randomColor, daysRemaining, randomUnoColor, randomColorName, numberToMonth, numberToShortMonth, lightBackgroundDarkTextRandom, smallerArray, arrayUniqueValue, thousandSeparated, thousandSeparatedToFixed, arrayUniqueValueOfArray, sortableAB, sortableBA, sortableAlgo, createElementFromHTML, htmlToElement, htmlToElements, array_move, getStatsBusiness, getStatsCustomer, getIp, getState, getTimeZone, getCountry, getCurrency, getIndustry, getWorkspaceType } from '../composables/info';
export { useMe, meAvatarInterface, MainUserInterface, MainUserSpacesInterface } from '../composables/me';
export { default as networkstatus } from '../composables/networkstatus';
export { default as notifications, useNotifications } from '../composables/notifications';
export { PERMISSION_CATEGORIES, PERMISSIONS, DEFAULT_ROLE_PERMISSIONS, usePermissions } from '../composables/permissions';
export { space, changeCurrentSpace, updateCurrentSpace, clearCurrentSpace } from '../composables/space';
export { stats, getUserStats, getSpaceStats, getSingleStats } from '../composables/stats';
export { useUploads } from '../composables/uploads';
export { useABTesting } from '../composables/useABTesting';
export { useAccountingIntegration, AccountingIntegration, SyncSettings, SyncRecord } from '../composables/useAccountingIntegration';
export { useAdAnalytics, AdAnalyticsSummary } from '../composables/useAdAnalytics';
export { useAdContent } from '../composables/useAdContent';
export { useAdInvoices } from '../composables/useAdInvoices';
export { useAdPayments } from '../composables/useAdPayments';
export { useAdSpotContent } from '../composables/useAdSpotContent';
export { useAdSpots } from '../composables/useAdSpots';
export { useAdSubscriptions } from '../composables/useAdSubscriptions';
export { useAdminAnalytics } from '../composables/useAdminAnalytics';
export { useAdvertisingROI, ROIAnalysis, ChannelROI, AudienceROI, CreativeROI } from '../composables/useAdvertisingROI';
export { useAnalytics } from '../composables/useAnalytics';
export { useAnalyticsNotifications, NotificationType, Notification } from '../composables/useAnalyticsNotifications';
export { useAuth } from '../composables/useAuth';
export { useAutomatedInvoicing } from '../composables/useAutomatedInvoicing';
export { useBackgroundSync } from '../composables/useBackgroundSync';
export { useCacheManager } from '../composables/useCacheManager';
export { useCacheOptimizer } from '../composables/useCacheOptimizer';
export { useCashFlowAnalytics } from '../composables/useCashFlowAnalytics';
export { useCurrentUser } from '../composables/useCurrentUser';
export { useDataRetention, RetentionPolicy, RetentionExecution } from '../composables/useDataRetention';
export { useEmail } from '../composables/useEmail';
export { useEmailRetry, EmailRetryAttempt, EmailRetryConfig } from '../composables/useEmailRetry';
export { useEmailTracking, EmailTrackingEvent, EmailMetrics } from '../composables/useEmailTracking';
export { useEmailUnsubscribe, UnsubscribeRecord, EmailPreferences } from '../composables/useEmailUnsubscribe';
export { useEngagementFunnel } from '../composables/useEngagementFunnel';
export { useFirebase } from '../composables/useFirebase';
export { useFormValidation, ValidationRule, FieldValidation, FormValidation } from '../composables/useFormValidation';
export { useGeographicAnalytics } from '../composables/useGeographicAnalytics';
export { useHaptics } from '../composables/useHaptics';
export { useInvoiceReminders, InvoiceReminder, ReminderTemplate } from '../composables/useInvoiceReminders';
export { useInvoiceWorkflows } from '../composables/useInvoiceWorkflows';
export { useMLPipeline, MLModel, PerformanceMetric, TrainingJob, TrainingConfig, TrainingResults } from '../composables/useMLPipeline';
export { useMobileNavigation } from '../composables/useMobileNavigation';
export { useMultiLanguageEmail, EmailTemplate, LanguageConfig } from '../composables/useMultiLanguageEmail';
export { DEFAULT_NOTIFICATION_PREFERENCES, validateNotificationFrequency, validateDayOfWeek, validateTimeOfDay, validateNotificationPreferences, useNotificationPreferences, NotificationFrequency, NotificationTypePreference, WeeklyReportPreference, NotificationPreferences } from '../composables/useNotificationPreferences';
export { useNotificationService, NotificationDocument } from '../composables/useNotificationService';
export { useOfflineStorage } from '../composables/useOfflineStorage';
export { usePDFGeneration } from '../composables/usePDFGeneration';
export { usePWAInstall } from '../composables/usePWAInstall';
export { usePaymentProcessing } from '../composables/usePaymentProcessing';
export { usePaymentReconciliation } from '../composables/usePaymentReconciliation';
export { usePredictiveAnalytics } from '../composables/usePredictiveAnalytics';
export { useSubscriptionBilling } from '../composables/useSubscriptionBilling';
export { useSyncQueue } from '../composables/useSyncQueue';
export { useTaxReporting, TaxReport, TaxLineItem, TaxConfiguration, TaxRate, ComplianceSettings } from '../composables/useTaxReporting';
export { useTouchGestures } from '../composables/useTouchGestures';
export { useUserProfile, UserProfile } from '../composables/useUserProfile';
export { useVisualization } from '../composables/useVisualization';
export { useWorkflowTemplates, WorkflowTemplate } from '../composables/useWorkflowTemplates';
export { useUserManagement } from '../composables/user-management';
export { user, changecurrentUser, updatecurrentUser, clearcurrentUser, setcurrentClient, changecurrentClient, updatecurrentClient, clearcurrentClient, checkIfContact, checkIfUser, getAccounts, getGmail, getClientBlock, getAccess } from '../composables/user';
export { required, email, url, phone, minLength, maxLength, numeric, alphanumeric, pattern, min, max } from '../composables/validationRules';
export { WebhookTester, WebhookTester, createWebhookTester, runAllWebhookTests, WebhookTestConfig, WebhookTestResult } from '../utils/webhook-tester';
export { usePWA, useTransparentPwaIcon, useMaskablePwaIcon, useFaviconPwaIcon, useApplePwaIcon, useAppleSplashScreenPwaIcon } from '../node_modules/@vite-pwa/nuxt/dist/runtime/composables/index';
export { useNuxtDevTools } from '../node_modules/@nuxt/devtools/dist/runtime/use-nuxt-devtools';
export { definePageMeta } from '../node_modules/nuxt/dist/pages/runtime/composables';