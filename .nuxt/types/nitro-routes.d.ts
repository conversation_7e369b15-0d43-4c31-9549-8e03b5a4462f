// Generated by nitro
import type { Serialize, Simplify } from "nitropack/types";
declare module "nitropack/types" {
  type Awaited<T> = T extends PromiseLike<infer U> ? Awaited<U> : T
  interface InternalApi {
    '/api/ab-tests/:id/events': {
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/ab-tests/[id]/events.post').default>>>>
    }
    '/api/ab-tests/:id/variant': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/ab-tests/[id]/variant.get').default>>>>
    }
    '/api/ab-tests': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/ab-tests/index.get').default>>>>
    }
    '/api/accounting/oauth/token': {
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/accounting/oauth/token.post').default>>>>
    }
    '/api/analytics/ad-click': {
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/analytics/ad-click.post').default>>>>
    }
    '/api/analytics/anomalies': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/analytics/anomalies.get').default>>>>
    }
    '/api/analytics/anomalies/:id/dismiss': {
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/analytics/anomalies/[id]/dismiss.post').default>>>>
    }
    '/api/analytics/benchmarks': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/analytics/benchmarks.get').default>>>>
    }
    '/api/analytics/predictions/engagement': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/analytics/predictions/engagement.get').default>>>>
    }
    '/api/analytics/recommendations/sharing': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/analytics/recommendations/sharing.get').default>>>>
    }
    '/api/analytics/track-duration': {
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/analytics/track-duration.post').default>>>>
    }
    '/api/covalonic/forms': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/covalonic/forms').default>>>>
    }
    '/api/email-tracking/click/:emailId': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/email-tracking/click/[emailId].get').default>>>>
    }
    '/api/email-tracking/pixel/:emailId': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/email-tracking/pixel/[emailId].get').default>>>>
    }
    '/api/email-tracking/webhook': {
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/email-tracking/webhook.post').default>>>>
    }
    '/api/email/send': {
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/email/send.post').default>>>>
    }
    '/api/firebase-test': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/firebase-test').default>>>>
    }
    '/api/firestore/add-multi': {
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/firestore/add-multi.post').default>>>>
    }
    '/api/firestore/add': {
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/firestore/add.post').default>>>>
    }
    '/api/firestore/delete-single': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/firestore/delete-single.get').default>>>>
    }
    '/api/firestore/delete': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/firestore/delete.get').default>>>>
    }
    '/api/firestore/increment': {
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/firestore/increment.post').default>>>>
    }
    '/api/firestore/login': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/firestore/login.get').default>>>>
    }
    '/api/firestore/logout': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/firestore/logout.get').default>>>>
    }
    '/api/firestore/query-order-by': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/firestore/query-order-by').default>>>>
    }
    '/api/firestore/query-single': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/firestore/query-single.get').default>>>>
    }
    '/api/firestore/query-where-double-limit': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/firestore/query-where-double-limit.get').default>>>>
    }
    '/api/firestore/query-where-limit': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/firestore/query-where-limit.get').default>>>>
    }
    '/api/firestore/query-where-tripple-limit': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/firestore/query-where-tripple-limit.get').default>>>>
    }
    '/api/firestore/query-where': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/firestore/query-where.get').default>>>>
    }
    '/api/firestore/query': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/firestore/query.get').default>>>>
    }
    '/api/firestore/register-and-login': {
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/firestore/register-and-login.post').default>>>>
    }
    '/api/firestore/register': {
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/firestore/register.post').default>>>>
    }
    '/api/firestore/remove-batch': {
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/firestore/remove-batch.post').default>>>>
    }
    '/api/firestore/set-batch-more': {
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/firestore/set-batch-more.post').default>>>>
    }
    '/api/firestore/set-batch': {
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/firestore/set-batch.post').default>>>>
    }
    '/api/firestore/set-bulk': {
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/firestore/set-bulk.post').default>>>>
    }
    '/api/firestore/set-single': {
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/firestore/set-single.post').default>>>>
    }
    '/api/firestore/set': {
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/firestore/set.post').default>>>>
    }
    '/api/firestore/update-single': {
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/firestore/update-single.post').default>>>>
    }
    '/api/firestore/upload': {
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/firestore/upload.post').default>>>>
    }
    '/api/flyers': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/flyers').default>>>>
    }
    '/api/invoices/:id': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/invoices/[id]/index.get').default>>>>
    }
    '/api/invoices/:id/regenerate-pdf': {
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/invoices/[id]/regenerate-pdf.post').default>>>>
    }
    '/api/invoices': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/invoices/index.get').default>>>>
    }
    '/api/notifications/send-push': {
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/notifications/send-push.post').default>>>>
    }
    '/api/payment/create-intent': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/payment/create-intent').default>>>>
    }
    '/api/payment/webhook': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/payment/webhook').default>>>>
    }
    '/api/scheduled/billing-processor': {
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/scheduled/billing-processor.post').default>>>>
    }
    '/api/scheduled/email-retry-processor': {
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/scheduled/email-retry-processor.post').default>>>>
    }
    '/api/scheduled/invoice-reminder-processor': {
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/scheduled/invoice-reminder-processor.post').default>>>>
    }
    '/api/send-notification': {
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/send-notification.post').default>>>>
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/send-notification').default>>>>
    }
    '/api/smtp/send': {
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/smtp/send.post').default>>>>
    }
    '/api/smtp/test': {
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/smtp/test.post').default>>>>
    }
    '/api/smtp/verify': {
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/smtp/verify.post').default>>>>
    }
    '/api/visualization/charts/:id/data': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/visualization/charts/[id]/data.get').default>>>>
    }
    '/api/visualization/charts': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/visualization/charts/index.get').default>>>>
    }
    '/api/webhooks/payment-completed': {
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/webhooks/payment-completed.post').default>>>>
    }
    '/__nuxt_error': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../node_modules/nuxt/dist/core/runtime/nitro/renderer').default>>>>
    }
  }
}
export {}