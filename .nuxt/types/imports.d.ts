// Generated by auto imports
export {}
declare global {
  const DEFAULT_NOTIFICATION_PREFERENCES: typeof import('../../composables/useNotificationPreferences')['DEFAULT_NOTIFICATION_PREFERENCES']
  const DEFAULT_ROLE_PERMISSIONS: typeof import('../../composables/permissions')['DEFAULT_ROLE_PERMISSIONS']
  const LightenDarkenColorHex: typeof import('../../composables/info')['LightenDarkenColorHex']
  const PERMISSIONS: typeof import('../../composables/permissions')['PERMISSIONS']
  const PERMISSION_CATEGORIES: typeof import('../../composables/permissions')['PERMISSION_CATEGORIES']
  const RGBToHex: typeof import('../../composables/info')['RGBToHex']
  const WebhookTester: typeof import('../../utils/webhook-tester')['WebhookTester']
  const abortNavigation: typeof import('../../node_modules/nuxt/dist/app/composables/router')['abortNavigation']
  const add: typeof import('../../composables/firebase')['add']
  const addFcmToken: typeof import('../../composables/firebase')['addFcmToken']
  const addRouteMiddleware: typeof import('../../node_modules/nuxt/dist/app/composables/router')['addRouteMiddleware']
  const alphanumeric: typeof import('../../composables/validationRules')['alphanumeric']
  const arrayUniqueValue: typeof import('../../composables/info')['arrayUniqueValue']
  const arrayUniqueValueOfArray: typeof import('../../composables/info')['arrayUniqueValueOfArray']
  const array_move: typeof import('../../composables/info')['array_move']
  const asyncComputed: typeof import('@vueuse/core')['asyncComputed']
  const autoResetRef: typeof import('@vueuse/core')['autoResetRef']
  const callOnce: typeof import('../../node_modules/nuxt/dist/app/composables/once')['callOnce']
  const cancelIdleCallback: typeof import('../../node_modules/nuxt/dist/app/compat/idle-callback')['cancelIdleCallback']
  const changeCurrentSpace: typeof import('../../composables/space')['changeCurrentSpace']
  const changecurrentClient: typeof import('../../composables/user')['changecurrentClient']
  const changecurrentUser: typeof import('../../composables/user')['changecurrentUser']
  const checkIfContact: typeof import('../../composables/user')['checkIfContact']
  const checkIfUser: typeof import('../../composables/user')['checkIfUser']
  const checkNotificationsEnabled: typeof import('../../composables/firebase')['checkNotificationsEnabled']
  const clearCurrentSpace: typeof import('../../composables/space')['clearCurrentSpace']
  const clearError: typeof import('../../node_modules/nuxt/dist/app/composables/error')['clearError']
  const clearNuxtData: typeof import('../../node_modules/nuxt/dist/app/composables/asyncData')['clearNuxtData']
  const clearNuxtState: typeof import('../../node_modules/nuxt/dist/app/composables/state')['clearNuxtState']
  const clearcurrentClient: typeof import('../../composables/user')['clearcurrentClient']
  const clearcurrentUser: typeof import('../../composables/user')['clearcurrentUser']
  const computed: typeof import('../../node_modules/vue')['computed']
  const computedAsync: typeof import('@vueuse/core')['computedAsync']
  const computedEager: typeof import('@vueuse/core')['computedEager']
  const computedInject: typeof import('@vueuse/core')['computedInject']
  const computedWithControl: typeof import('@vueuse/core')['computedWithControl']
  const controlledComputed: typeof import('@vueuse/core')['controlledComputed']
  const controlledRef: typeof import('@vueuse/core')['controlledRef']
  const createElementFromHTML: typeof import('../../composables/info')['createElementFromHTML']
  const createEmail: typeof import('../../composables/email')['createEmail']
  const createError: typeof import('../../node_modules/nuxt/dist/app/composables/error')['createError']
  const createEventHook: typeof import('@vueuse/core')['createEventHook']
  const createGlobalState: typeof import('@vueuse/core')['createGlobalState']
  const createInjectionState: typeof import('@vueuse/core')['createInjectionState']
  const createReactiveFn: typeof import('@vueuse/core')['createReactiveFn']
  const createReusableTemplate: typeof import('@vueuse/core')['createReusableTemplate']
  const createSharedComposable: typeof import('@vueuse/core')['createSharedComposable']
  const createTemplatePromise: typeof import('@vueuse/core')['createTemplatePromise']
  const createUnrefFn: typeof import('@vueuse/core')['createUnrefFn']
  const createWebhookTester: typeof import('../../utils/webhook-tester')['createWebhookTester']
  const customRef: typeof import('../../node_modules/vue')['customRef']
  const database: typeof import('../../composables/database')['database']
  const daysRemaining: typeof import('../../composables/info')['daysRemaining']
  const debouncedRef: typeof import('@vueuse/core')['debouncedRef']
  const debouncedWatch: typeof import('@vueuse/core')['debouncedWatch']
  const defineAppConfig: typeof import('../../node_modules/nuxt/dist/app/nuxt')['defineAppConfig']
  const defineAsyncComponent: typeof import('../../node_modules/vue')['defineAsyncComponent']
  const defineComponent: typeof import('../../node_modules/vue')['defineComponent']
  const defineNuxtComponent: typeof import('../../node_modules/nuxt/dist/app/composables/component')['defineNuxtComponent']
  const defineNuxtLink: typeof import('../../node_modules/nuxt/dist/app/components/nuxt-link')['defineNuxtLink']
  const defineNuxtPlugin: typeof import('../../node_modules/nuxt/dist/app/nuxt')['defineNuxtPlugin']
  const defineNuxtRouteMiddleware: typeof import('../../node_modules/nuxt/dist/app/composables/router')['defineNuxtRouteMiddleware']
  const definePageMeta: typeof import('../../node_modules/nuxt/dist/pages/runtime/composables')['definePageMeta']
  const definePayloadPlugin: typeof import('../../node_modules/nuxt/dist/app/nuxt')['definePayloadPlugin']
  const definePayloadReducer: typeof import('../../node_modules/nuxt/dist/app/composables/payload')['definePayloadReducer']
  const definePayloadReviver: typeof import('../../node_modules/nuxt/dist/app/composables/payload')['definePayloadReviver']
  const del: typeof import('../../composables/firebase')['del']
  const deleteBatch: typeof import('../../composables/firebase')['deleteBatch']
  const deleteById: typeof import('../../composables/firebase')['deleteById']
  const deleteItems: typeof import('../../composables/info')['deleteItems']
  const eagerComputed: typeof import('@vueuse/core')['eagerComputed']
  const effect: typeof import('../../node_modules/vue')['effect']
  const effectScope: typeof import('../../node_modules/vue')['effectScope']
  const email: typeof import('../../composables/validationRules')['email']
  const extendRef: typeof import('@vueuse/core')['extendRef']
  const extraInfo: typeof import('../../composables/info')['extraInfo']
  const extraInfoUpdate: typeof import('../../composables/info')['extraInfoUpdate']
  const extraUpdateInfo: typeof import('../../composables/info')['extraUpdateInfo']
  const formatPhoneNumber: typeof import('../../composables/info')['formatPhoneNumber']
  const formatPhoneNumberPlus: typeof import('../../composables/info')['formatPhoneNumberPlus']
  const formatter: typeof import('../../composables/info')['formatter']
  const getAccess: typeof import('../../composables/user')['getAccess']
  const getAccounts: typeof import('../../composables/user')['getAccounts']
  const getAppManifest: typeof import('../../node_modules/nuxt/dist/app/composables/manifest')['getAppManifest']
  const getClientBlock: typeof import('../../composables/user')['getClientBlock']
  const getCountry: typeof import('../../composables/info')['getCountry']
  const getCurrency: typeof import('../../composables/info')['getCurrency']
  const getCurrentInstance: typeof import('../../node_modules/vue')['getCurrentInstance']
  const getCurrentScope: typeof import('../../node_modules/vue')['getCurrentScope']
  const getGmail: typeof import('../../composables/user')['getGmail']
  const getIndustry: typeof import('../../composables/info')['getIndustry']
  const getIp: typeof import('../../composables/info')['getIp']
  const getMeta: typeof import('../../composables/info')['getMeta']
  const getRandomInt: typeof import('../../composables/info')['getRandomInt']
  const getRouteRules: typeof import('../../node_modules/nuxt/dist/app/composables/manifest')['getRouteRules']
  const getSingleStats: typeof import('../../composables/stats')['getSingleStats']
  const getSpaceStats: typeof import('../../composables/stats')['getSpaceStats']
  const getSrc: typeof import('../../composables/info')['getSrc']
  const getState: typeof import('../../composables/info')['getState']
  const getStatsBusiness: typeof import('../../composables/info')['getStatsBusiness']
  const getStatsCustomer: typeof import('../../composables/info')['getStatsCustomer']
  const getTimeZone: typeof import('../../composables/info')['getTimeZone']
  const getUserStats: typeof import('../../composables/stats')['getUserStats']
  const getWorkspaceType: typeof import('../../composables/info')['getWorkspaceType']
  const h: typeof import('../../node_modules/vue')['h']
  const hasInjectionContext: typeof import('../../node_modules/vue')['hasInjectionContext']
  const hasRole: typeof import('../../composables/info')['hasRole']
  const hexToHSL: typeof import('../../composables/info')['hexToHSL']
  const hexToRGBA: typeof import('../../composables/info')['hexToRGBA']
  const hexToRgbA: typeof import('../../composables/info')['hexToRgbA']
  const htmlToElement: typeof import('../../composables/info')['htmlToElement']
  const htmlToElements: typeof import('../../composables/info')['htmlToElements']
  const ignorableWatch: typeof import('@vueuse/core')['ignorableWatch']
  const incrementValue: typeof import('../../composables/firebase')['incrementValue']
  const inject: typeof import('../../node_modules/vue')['inject']
  const injectHead: typeof import('../../node_modules/@unhead/vue')['injectHead']
  const injectLocal: typeof import('@vueuse/core')['injectLocal']
  const isAdmin: typeof import('../../composables/info')['isAdmin']
  const isDefined: typeof import('@vueuse/core')['isDefined']
  const isNuxtError: typeof import('../../node_modules/nuxt/dist/app/composables/error')['isNuxtError']
  const isPrerendered: typeof import('../../node_modules/nuxt/dist/app/composables/payload')['isPrerendered']
  const isProxy: typeof import('../../node_modules/vue')['isProxy']
  const isReactive: typeof import('../../node_modules/vue')['isReactive']
  const isReadonly: typeof import('../../node_modules/vue')['isReadonly']
  const isRef: typeof import('../../node_modules/vue')['isRef']
  const isShallow: typeof import('../../node_modules/vue')['isShallow']
  const isSuperAdmin: typeof import('../../composables/info')['isSuperAdmin']
  const isVue2: typeof import('../../node_modules/nuxt/dist/app/compat/vue-demi')['isVue2']
  const isVue3: typeof import('../../node_modules/nuxt/dist/app/compat/vue-demi')['isVue3']
  const lightBackgroundDarkTextRandom: typeof import('../../composables/info')['lightBackgroundDarkTextRandom']
  const loadPayload: typeof import('../../node_modules/nuxt/dist/app/composables/payload')['loadPayload']
  const logBatteryInfo: typeof import('../../composables/info')['logBatteryInfo']
  const loginUser: typeof import('../../composables/firebase')['loginUser']
  const logoutUser: typeof import('../../composables/firebase')['logoutUser']
  const makeDateTimeReadable: typeof import('../../composables/info')['makeDateTimeReadable']
  const makeDateTimeUserReadable: typeof import('../../composables/info')['makeDateTimeUserReadable']
  const makeDestructurable: typeof import('@vueuse/core')['makeDestructurable']
  const makeid: typeof import('../../composables/info')['makeid']
  const markRaw: typeof import('../../node_modules/vue')['markRaw']
  const max: typeof import('../../composables/validationRules')['max']
  const maxLength: typeof import('../../composables/validationRules')['maxLength']
  const mergeModels: typeof import('../../node_modules/vue')['mergeModels']
  const min: typeof import('../../composables/validationRules')['min']
  const minLength: typeof import('../../composables/validationRules')['minLength']
  const navigateTo: typeof import('../../node_modules/nuxt/dist/app/composables/router')['navigateTo']
  const networkstatus: typeof import('../../composables/networkstatus')['default']
  const nextTick: typeof import('../../node_modules/vue')['nextTick']
  const notifications: typeof import('../../composables/notifications')['default']
  const numberToMonth: typeof import('../../composables/info')['numberToMonth']
  const numberToShortMonth: typeof import('../../composables/info')['numberToShortMonth']
  const numeric: typeof import('../../composables/validationRules')['numeric']
  const onActivated: typeof import('../../node_modules/vue')['onActivated']
  const onBeforeMount: typeof import('../../node_modules/vue')['onBeforeMount']
  const onBeforeRouteLeave: typeof import('../../node_modules/vue-router')['onBeforeRouteLeave']
  const onBeforeRouteUpdate: typeof import('../../node_modules/vue-router')['onBeforeRouteUpdate']
  const onBeforeUnmount: typeof import('../../node_modules/vue')['onBeforeUnmount']
  const onBeforeUpdate: typeof import('../../node_modules/vue')['onBeforeUpdate']
  const onClickOutside: typeof import('@vueuse/core')['onClickOutside']
  const onDeactivated: typeof import('../../node_modules/vue')['onDeactivated']
  const onErrorCaptured: typeof import('../../node_modules/vue')['onErrorCaptured']
  const onKeyStroke: typeof import('@vueuse/core')['onKeyStroke']
  const onLongPress: typeof import('@vueuse/core')['onLongPress']
  const onMounted: typeof import('../../node_modules/vue')['onMounted']
  const onNuxtReady: typeof import('../../node_modules/nuxt/dist/app/composables/ready')['onNuxtReady']
  const onPrehydrate: typeof import('../../node_modules/nuxt/dist/app/composables/ssr')['onPrehydrate']
  const onRenderTracked: typeof import('../../node_modules/vue')['onRenderTracked']
  const onRenderTriggered: typeof import('../../node_modules/vue')['onRenderTriggered']
  const onScopeDispose: typeof import('../../node_modules/vue')['onScopeDispose']
  const onServerPrefetch: typeof import('../../node_modules/vue')['onServerPrefetch']
  const onStartTyping: typeof import('@vueuse/core')['onStartTyping']
  const onUnmounted: typeof import('../../node_modules/vue')['onUnmounted']
  const onUpdated: typeof import('../../node_modules/vue')['onUpdated']
  const pattern: typeof import('../../composables/validationRules')['pattern']
  const pausableWatch: typeof import('@vueuse/core')['pausableWatch']
  const phone: typeof import('../../composables/validationRules')['phone']
  const prefetchComponents: typeof import('../../node_modules/nuxt/dist/app/composables/preload')['prefetchComponents']
  const preloadComponents: typeof import('../../node_modules/nuxt/dist/app/composables/preload')['preloadComponents']
  const preloadPayload: typeof import('../../node_modules/nuxt/dist/app/composables/payload')['preloadPayload']
  const preloadRouteComponents: typeof import('../../node_modules/nuxt/dist/app/composables/preload')['preloadRouteComponents']
  const prerenderRoutes: typeof import('../../node_modules/nuxt/dist/app/composables/ssr')['prerenderRoutes']
  const provide: typeof import('../../node_modules/vue')['provide']
  const provideLocal: typeof import('@vueuse/core')['provideLocal']
  const proxyRefs: typeof import('../../node_modules/vue')['proxyRefs']
  const queryByCollection: typeof import('../../composables/firebase')['queryByCollection']
  const queryByCollectionLimit: typeof import('../../composables/firebase')['queryByCollectionLimit']
  const queryById: typeof import('../../composables/firebase')['queryById']
  const queryByWhere2: typeof import('../../composables/firebase')['queryByWhere2']
  const queryByWhere: typeof import('../../composables/firebase')['queryByWhere']
  const queryByWhereDouble: typeof import('../../composables/firebase')['queryByWhereDouble']
  const queryByWhereDoubleLimit: typeof import('../../composables/firebase')['queryByWhereDoubleLimit']
  const queryByWhereFar: typeof import('../../composables/firebase')['queryByWhereFar']
  const queryByWhereLimit: typeof import('../../composables/firebase')['queryByWhereLimit']
  const queryByWhereTrippleLimit: typeof import('../../composables/firebase')['queryByWhereTrippleLimit']
  const randomBotUrl: typeof import('../../composables/info')['randomBotUrl']
  const randomColor: typeof import('../../composables/info')['randomColor']
  const randomColorName: typeof import('../../composables/info')['randomColorName']
  const randomItemInArray: typeof import('../../composables/info')['randomItemInArray']
  const randomPerson: typeof import('../../composables/info')['randomPerson']
  const randomUnoColor: typeof import('../../composables/info')['randomUnoColor']
  const reactify: typeof import('@vueuse/core')['reactify']
  const reactifyObject: typeof import('@vueuse/core')['reactifyObject']
  const reactive: typeof import('../../node_modules/vue')['reactive']
  const reactiveComputed: typeof import('@vueuse/core')['reactiveComputed']
  const reactiveOmit: typeof import('@vueuse/core')['reactiveOmit']
  const reactivePick: typeof import('@vueuse/core')['reactivePick']
  const readonly: typeof import('../../node_modules/vue')['readonly']
  const ref: typeof import('../../node_modules/vue')['ref']
  const refAutoReset: typeof import('@vueuse/core')['refAutoReset']
  const refDebounced: typeof import('@vueuse/core')['refDebounced']
  const refDefault: typeof import('@vueuse/core')['refDefault']
  const refThrottled: typeof import('@vueuse/core')['refThrottled']
  const refWithControl: typeof import('@vueuse/core')['refWithControl']
  const refreshCookie: typeof import('../../node_modules/nuxt/dist/app/composables/cookie')['refreshCookie']
  const refreshNuxtData: typeof import('../../node_modules/nuxt/dist/app/composables/asyncData')['refreshNuxtData']
  const registerUser: typeof import('../../composables/firebase')['registerUser']
  const reloadNuxtApp: typeof import('../../node_modules/nuxt/dist/app/composables/chunk')['reloadNuxtApp']
  const removeExtra: typeof import('../../composables/info')['removeExtra']
  const removeUndefined: typeof import('../../composables/info')['removeUndefined']
  const requestIdleCallback: typeof import('../../node_modules/nuxt/dist/app/compat/idle-callback')['requestIdleCallback']
  const required: typeof import('../../composables/validationRules')['required']
  const resolveComponent: typeof import('../../node_modules/vue')['resolveComponent']
  const resolveRef: typeof import('@vueuse/core')['resolveRef']
  const resolveUnref: typeof import('@vueuse/core')['resolveUnref']
  const runAllWebhookTests: typeof import('../../utils/webhook-tester')['runAllWebhookTests']
  const schemaHeaders: typeof import('../../composables/forms')['schemaHeaders']
  const schemaSkin: typeof import('../../composables/forms')['schemaSkin']
  const sendEmail: typeof import('../../composables/email')['sendEmail']
  const set: typeof import('../../composables/firebase')['set']
  const setBatch: typeof import('../../composables/firebase')['setBatch']
  const setBatchMore: typeof import('../../composables/firebase')['setBatchMore']
  const setById: typeof import('../../composables/firebase')['setById']
  const setInterval: typeof import('../../node_modules/nuxt/dist/app/compat/interval')['setInterval']
  const setPageLayout: typeof import('../../node_modules/nuxt/dist/app/composables/router')['setPageLayout']
  const setResponseStatus: typeof import('../../node_modules/nuxt/dist/app/composables/ssr')['setResponseStatus']
  const setcurrentClient: typeof import('../../composables/user')['setcurrentClient']
  const shallowReactive: typeof import('../../node_modules/vue')['shallowReactive']
  const shallowReadonly: typeof import('../../node_modules/vue')['shallowReadonly']
  const shallowRef: typeof import('../../node_modules/vue')['shallowRef']
  const showError: typeof import('../../node_modules/nuxt/dist/app/composables/error')['showError']
  const smallerArray: typeof import('../../composables/info')['smallerArray']
  const sortableAB: typeof import('../../composables/info')['sortableAB']
  const sortableAlgo: typeof import('../../composables/info')['sortableAlgo']
  const sortableBA: typeof import('../../composables/info')['sortableBA']
  const space: typeof import('../../composables/space')['space']
  const stats: typeof import('../../composables/stats')['stats']
  const syncRef: typeof import('@vueuse/core')['syncRef']
  const syncRefs: typeof import('@vueuse/core')['syncRefs']
  const templateRef: typeof import('@vueuse/core')['templateRef']
  const thousandSeparated: typeof import('../../composables/info')['thousandSeparated']
  const thousandSeparatedToFixed: typeof import('../../composables/info')['thousandSeparatedToFixed']
  const throttledRef: typeof import('@vueuse/core')['throttledRef']
  const throttledWatch: typeof import('@vueuse/core')['throttledWatch']
  const toRaw: typeof import('../../node_modules/vue')['toRaw']
  const toReactive: typeof import('@vueuse/core')['toReactive']
  const toRef: typeof import('../../node_modules/vue')['toRef']
  const toRefs: typeof import('../../node_modules/vue')['toRefs']
  const toValue: typeof import('../../node_modules/vue')['toValue']
  const toggleUserStatus: typeof import('../../composables/firebase')['toggleUserStatus']
  const triggerRef: typeof import('../../node_modules/vue')['triggerRef']
  const tryOnBeforeMount: typeof import('@vueuse/core')['tryOnBeforeMount']
  const tryOnBeforeUnmount: typeof import('@vueuse/core')['tryOnBeforeUnmount']
  const tryOnMounted: typeof import('@vueuse/core')['tryOnMounted']
  const tryOnScopeDispose: typeof import('@vueuse/core')['tryOnScopeDispose']
  const tryOnUnmounted: typeof import('@vueuse/core')['tryOnUnmounted']
  const tryUseNuxtApp: typeof import('../../node_modules/nuxt/dist/app/nuxt')['tryUseNuxtApp']
  const unref: typeof import('../../node_modules/vue')['unref']
  const unrefElement: typeof import('@vueuse/core')['unrefElement']
  const until: typeof import('@vueuse/core')['until']
  const update: typeof import('../../composables/firebase')['update']
  const updateAppConfig: typeof import('../../node_modules/nuxt/dist/app/config')['updateAppConfig']
  const updateBatch: typeof import('../../composables/firebase')['updateBatch']
  const updateCurrentSpace: typeof import('../../composables/space')['updateCurrentSpace']
  const updateRoot: typeof import('../../composables/info')['updateRoot']
  const updateUserRole: typeof import('../../composables/firebase')['updateUserRole']
  const updatecurrentClient: typeof import('../../composables/user')['updatecurrentClient']
  const updatecurrentUser: typeof import('../../composables/user')['updatecurrentUser']
  const upload: typeof import('../../composables/firebase')['upload']
  const url: typeof import('../../composables/validationRules')['url']
  const useABTesting: typeof import('../../composables/useABTesting')['useABTesting']
  const useAccountingIntegration: typeof import('../../composables/useAccountingIntegration')['useAccountingIntegration']
  const useActiveElement: typeof import('@vueuse/core')['useActiveElement']
  const useActivityTracking: typeof import('../../composables/activity-tracking')['useActivityTracking']
  const useAdAnalytics: typeof import('../../composables/useAdAnalytics')['useAdAnalytics']
  const useAdContent: typeof import('../../composables/useAdContent')['useAdContent']
  const useAdInvoices: typeof import('../../composables/useAdInvoices')['useAdInvoices']
  const useAdPayments: typeof import('../../composables/useAdPayments')['useAdPayments']
  const useAdSpotContent: typeof import('../../composables/useAdSpotContent')['useAdSpotContent']
  const useAdSpots: typeof import('../../composables/useAdSpots')['useAdSpots']
  const useAdSubscriptions: typeof import('../../composables/useAdSubscriptions')['useAdSubscriptions']
  const useAdminAnalytics: typeof import('../../composables/useAdminAnalytics')['useAdminAnalytics']
  const useAdminDashboard: typeof import('../../composables/admin-dashboard')['useAdminDashboard']
  const useAdvertisingROI: typeof import('../../composables/useAdvertisingROI')['useAdvertisingROI']
  const useAnalytics: typeof import('../../composables/useAnalytics')['useAnalytics']
  const useAnalyticsNotifications: typeof import('../../composables/useAnalyticsNotifications')['useAnalyticsNotifications']
  const useAnalyticsTracking: typeof import('../../composables/analytics-tracking')['useAnalyticsTracking']
  const useAnimate: typeof import('@vueuse/core')['useAnimate']
  const useAppConfig: typeof import('../../node_modules/nuxt/dist/app/config')['useAppConfig']
  const useApplePwaIcon: typeof import('../../node_modules/@vite-pwa/nuxt/dist/runtime/composables/index')['useApplePwaIcon']
  const useAppleSplashScreenPwaIcon: typeof import('../../node_modules/@vite-pwa/nuxt/dist/runtime/composables/index')['useAppleSplashScreenPwaIcon']
  const useArrayDifference: typeof import('@vueuse/core')['useArrayDifference']
  const useArrayEvery: typeof import('@vueuse/core')['useArrayEvery']
  const useArrayFilter: typeof import('@vueuse/core')['useArrayFilter']
  const useArrayFind: typeof import('@vueuse/core')['useArrayFind']
  const useArrayFindIndex: typeof import('@vueuse/core')['useArrayFindIndex']
  const useArrayFindLast: typeof import('@vueuse/core')['useArrayFindLast']
  const useArrayIncludes: typeof import('@vueuse/core')['useArrayIncludes']
  const useArrayJoin: typeof import('@vueuse/core')['useArrayJoin']
  const useArrayMap: typeof import('@vueuse/core')['useArrayMap']
  const useArrayReduce: typeof import('@vueuse/core')['useArrayReduce']
  const useArraySome: typeof import('@vueuse/core')['useArraySome']
  const useArrayUnique: typeof import('@vueuse/core')['useArrayUnique']
  const useAsyncData: typeof import('../../node_modules/nuxt/dist/app/composables/asyncData')['useAsyncData']
  const useAsyncQueue: typeof import('@vueuse/core')['useAsyncQueue']
  const useAsyncState: typeof import('@vueuse/core')['useAsyncState']
  const useAttrs: typeof import('../../node_modules/vue')['useAttrs']
  const useAuth: typeof import('../../composables/useAuth')['useAuth']
  const useAuthUtils: typeof import('../../composables/auth-utils')['useAuthUtils']
  const useAutomatedInvoicing: typeof import('../../composables/useAutomatedInvoicing')['useAutomatedInvoicing']
  const useBackgroundSync: typeof import('../../composables/useBackgroundSync')['useBackgroundSync']
  const useBase64: typeof import('@vueuse/core')['useBase64']
  const useBattery: typeof import('@vueuse/core')['useBattery']
  const useBlogs: typeof import('../../composables/blog')['useBlogs']
  const useBluetooth: typeof import('@vueuse/core')['useBluetooth']
  const useBreakpoints: typeof import('@vueuse/core')['useBreakpoints']
  const useBroadcastChannel: typeof import('@vueuse/core')['useBroadcastChannel']
  const useBrowserLocation: typeof import('@vueuse/core')['useBrowserLocation']
  const useBrowserUrl: typeof import('../../composables/info')['useBrowserUrl']
  const useBuilderComponents: typeof import('../../composables/builder-components')['useBuilderComponents']
  const useCRM: typeof import('../../composables/crm')['useCRM']
  const useCacheManager: typeof import('../../composables/useCacheManager')['useCacheManager']
  const useCacheOptimizer: typeof import('../../composables/useCacheOptimizer')['useCacheOptimizer']
  const useCached: typeof import('@vueuse/core')['useCached']
  const useCashFlowAnalytics: typeof import('../../composables/useCashFlowAnalytics')['useCashFlowAnalytics']
  const useClipboard: typeof import('@vueuse/core')['useClipboard']
  const useClipboardItems: typeof import('@vueuse/core')['useClipboardItems']
  const useCloned: typeof import('@vueuse/core')['useCloned']
  const useColorMode: typeof import('@vueuse/core')['useColorMode']
  const useConfirmDialog: typeof import('@vueuse/core')['useConfirmDialog']
  const useContent: typeof import('../../composables/content')['useContent']
  const useContentModeration: typeof import('../../composables/content-moderation')['useContentModeration']
  const useCookie: typeof import('../../node_modules/nuxt/dist/app/composables/cookie')['useCookie']
  const useCounter: typeof import('@vueuse/core')['useCounter']
  const useCssModule: typeof import('../../node_modules/vue')['useCssModule']
  const useCssVar: typeof import('@vueuse/core')['useCssVar']
  const useCssVars: typeof import('../../node_modules/vue')['useCssVars']
  const useCurrentElement: typeof import('@vueuse/core')['useCurrentElement']
  const useCurrentUser: typeof import('../../composables/useCurrentUser')['useCurrentUser']
  const useCycleList: typeof import('@vueuse/core')['useCycleList']
  const useDark: typeof import('@vueuse/core')['useDark']
  const useDataRetention: typeof import('../../composables/useDataRetention')['useDataRetention']
  const useDateFormat: typeof import('@vueuse/core')['useDateFormat']
  const useDebounce: typeof import('@vueuse/core')['useDebounce']
  const useDebounceFn: typeof import('@vueuse/core')['useDebounceFn']
  const useDebouncedRefHistory: typeof import('@vueuse/core')['useDebouncedRefHistory']
  const useDeviceMotion: typeof import('@vueuse/core')['useDeviceMotion']
  const useDeviceOrientation: typeof import('@vueuse/core')['useDeviceOrientation']
  const useDevicePixelRatio: typeof import('@vueuse/core')['useDevicePixelRatio']
  const useDevicesList: typeof import('@vueuse/core')['useDevicesList']
  const useDisplayMedia: typeof import('@vueuse/core')['useDisplayMedia']
  const useDocumentVisibility: typeof import('@vueuse/core')['useDocumentVisibility']
  const useDraggable: typeof import('@vueuse/core')['useDraggable']
  const useDropZone: typeof import('@vueuse/core')['useDropZone']
  const useElementBounding: typeof import('@vueuse/core')['useElementBounding']
  const useElementByPoint: typeof import('@vueuse/core')['useElementByPoint']
  const useElementHover: typeof import('@vueuse/core')['useElementHover']
  const useElementSize: typeof import('@vueuse/core')['useElementSize']
  const useElementVisibility: typeof import('@vueuse/core')['useElementVisibility']
  const useEmail: typeof import('../../composables/useEmail')['useEmail']
  const useEmailRetry: typeof import('../../composables/useEmailRetry')['useEmailRetry']
  const useEmailTracking: typeof import('../../composables/useEmailTracking')['useEmailTracking']
  const useEmailUnsubscribe: typeof import('../../composables/useEmailUnsubscribe')['useEmailUnsubscribe']
  const useEngagementFunnel: typeof import('../../composables/useEngagementFunnel')['useEngagementFunnel']
  const useError: typeof import('../../node_modules/nuxt/dist/app/composables/error')['useError']
  const useEventBus: typeof import('@vueuse/core')['useEventBus']
  const useEventListener: typeof import('@vueuse/core')['useEventListener']
  const useEventSource: typeof import('@vueuse/core')['useEventSource']
  const useEyeDropper: typeof import('@vueuse/core')['useEyeDropper']
  const useFavicon: typeof import('@vueuse/core')['useFavicon']
  const useFaviconPwaIcon: typeof import('../../node_modules/@vite-pwa/nuxt/dist/runtime/composables/index')['useFaviconPwaIcon']
  const useFetch: typeof import('../../node_modules/nuxt/dist/app/composables/fetch')['useFetch']
  const useFileDialog: typeof import('@vueuse/core')['useFileDialog']
  const useFileSystemAccess: typeof import('@vueuse/core')['useFileSystemAccess']
  const useFirebase: typeof import('../../composables/useFirebase')['useFirebase']
  const useFocus: typeof import('@vueuse/core')['useFocus']
  const useFocusWithin: typeof import('@vueuse/core')['useFocusWithin']
  const useFormValidation: typeof import('../../composables/useFormValidation')['useFormValidation']
  const useFps: typeof import('@vueuse/core')['useFps']
  const useFullscreen: typeof import('@vueuse/core')['useFullscreen']
  const useGamepad: typeof import('@vueuse/core')['useGamepad']
  const useGeographicAnalytics: typeof import('../../composables/useGeographicAnalytics')['useGeographicAnalytics']
  const useGeolocation: typeof import('@vueuse/core')['useGeolocation']
  const useGeolocationWithFallback: typeof import('../../composables/geolocation')['useGeolocationWithFallback']
  const useHaptics: typeof import('../../composables/useHaptics')['useHaptics']
  const useHead: typeof import('../../node_modules/@unhead/vue')['useHead']
  const useHeadSafe: typeof import('../../node_modules/@unhead/vue')['useHeadSafe']
  const useHydration: typeof import('../../node_modules/nuxt/dist/app/composables/hydrate')['useHydration']
  const useId: typeof import('../../node_modules/vue')['useId']
  const useIdle: typeof import('@vueuse/core')['useIdle']
  const useInfiniteScroll: typeof import('@vueuse/core')['useInfiniteScroll']
  const useIntersectionObserver: typeof import('@vueuse/core')['useIntersectionObserver']
  const useInterval: typeof import('@vueuse/core')['useInterval']
  const useIntervalFn: typeof import('@vueuse/core')['useIntervalFn']
  const useInvoiceReminders: typeof import('../../composables/useInvoiceReminders')['useInvoiceReminders']
  const useInvoiceWorkflows: typeof import('../../composables/useInvoiceWorkflows')['useInvoiceWorkflows']
  const useKeyModifier: typeof import('@vueuse/core')['useKeyModifier']
  const useLastChanged: typeof import('@vueuse/core')['useLastChanged']
  const useLazyAsyncData: typeof import('../../node_modules/nuxt/dist/app/composables/asyncData')['useLazyAsyncData']
  const useLazyFetch: typeof import('../../node_modules/nuxt/dist/app/composables/fetch')['useLazyFetch']
  const useLink: typeof import('../../node_modules/vue-router')['useLink']
  const useLoadingIndicator: typeof import('../../node_modules/nuxt/dist/app/composables/loading-indicator')['useLoadingIndicator']
  const useLocalStorage: typeof import('@vueuse/core')['useLocalStorage']
  const useMLPipeline: typeof import('../../composables/useMLPipeline')['useMLPipeline']
  const useMagicKeys: typeof import('@vueuse/core')['useMagicKeys']
  const useManualRefHistory: typeof import('@vueuse/core')['useManualRefHistory']
  const useMaskablePwaIcon: typeof import('../../node_modules/@vite-pwa/nuxt/dist/runtime/composables/index')['useMaskablePwaIcon']
  const useMe: typeof import('../../composables/me')['useMe']
  const useMediaControls: typeof import('@vueuse/core')['useMediaControls']
  const useMediaQuery: typeof import('@vueuse/core')['useMediaQuery']
  const useMemoize: typeof import('@vueuse/core')['useMemoize']
  const useMemory: typeof import('@vueuse/core')['useMemory']
  const useMobileNavigation: typeof import('../../composables/useMobileNavigation')['useMobileNavigation']
  const useModel: typeof import('../../node_modules/vue')['useModel']
  const useMounted: typeof import('@vueuse/core')['useMounted']
  const useMouse: typeof import('@vueuse/core')['useMouse']
  const useMouseInElement: typeof import('@vueuse/core')['useMouseInElement']
  const useMousePressed: typeof import('@vueuse/core')['useMousePressed']
  const useMultiLanguageEmail: typeof import('../../composables/useMultiLanguageEmail')['useMultiLanguageEmail']
  const useMutationObserver: typeof import('@vueuse/core')['useMutationObserver']
  const useNavigatorLanguage: typeof import('@vueuse/core')['useNavigatorLanguage']
  const useNetwork: typeof import('@vueuse/core')['useNetwork']
  const useNotificationPreferences: typeof import('../../composables/useNotificationPreferences')['useNotificationPreferences']
  const useNotificationService: typeof import('../../composables/useNotificationService')['useNotificationService']
  const useNotifications: typeof import('../../composables/notifications')['useNotifications']
  const useNow: typeof import('@vueuse/core')['useNow']
  const useNuxtApp: typeof import('../../node_modules/nuxt/dist/app/nuxt')['useNuxtApp']
  const useNuxtData: typeof import('../../node_modules/nuxt/dist/app/composables/asyncData')['useNuxtData']
  const useNuxtDevTools: typeof import('../../node_modules/@nuxt/devtools/dist/runtime/use-nuxt-devtools')['useNuxtDevTools']
  const useObjectUrl: typeof import('@vueuse/core')['useObjectUrl']
  const useOfflineStorage: typeof import('../../composables/useOfflineStorage')['useOfflineStorage']
  const useOffsetPagination: typeof import('@vueuse/core')['useOffsetPagination']
  const useOnline: typeof import('@vueuse/core')['useOnline']
  const usePDFGeneration: typeof import('../../composables/usePDFGeneration')['usePDFGeneration']
  const usePWA: typeof import('../../node_modules/@vite-pwa/nuxt/dist/runtime/composables/index')['usePWA']
  const usePWAInstall: typeof import('../../composables/usePWAInstall')['usePWAInstall']
  const usePageLeave: typeof import('@vueuse/core')['usePageLeave']
  const useParallax: typeof import('@vueuse/core')['useParallax']
  const useParentElement: typeof import('@vueuse/core')['useParentElement']
  const usePaymentProcessing: typeof import('../../composables/usePaymentProcessing')['usePaymentProcessing']
  const usePaymentReconciliation: typeof import('../../composables/usePaymentReconciliation')['usePaymentReconciliation']
  const usePerformanceObserver: typeof import('@vueuse/core')['usePerformanceObserver']
  const usePermission: typeof import('@vueuse/core')['usePermission']
  const usePermissions: typeof import('../../composables/permissions')['usePermissions']
  const usePointer: typeof import('@vueuse/core')['usePointer']
  const usePointerLock: typeof import('@vueuse/core')['usePointerLock']
  const usePointerSwipe: typeof import('@vueuse/core')['usePointerSwipe']
  const usePredictiveAnalytics: typeof import('../../composables/usePredictiveAnalytics')['usePredictiveAnalytics']
  const usePreferredColorScheme: typeof import('@vueuse/core')['usePreferredColorScheme']
  const usePreferredContrast: typeof import('@vueuse/core')['usePreferredContrast']
  const usePreferredDark: typeof import('@vueuse/core')['usePreferredDark']
  const usePreferredLanguages: typeof import('@vueuse/core')['usePreferredLanguages']
  const usePreferredReducedMotion: typeof import('@vueuse/core')['usePreferredReducedMotion']
  const usePreviewMode: typeof import('../../node_modules/nuxt/dist/app/composables/preview')['usePreviewMode']
  const usePrevious: typeof import('@vueuse/core')['usePrevious']
  const useRafFn: typeof import('@vueuse/core')['useRafFn']
  const useRefHistory: typeof import('@vueuse/core')['useRefHistory']
  const useRequestEvent: typeof import('../../node_modules/nuxt/dist/app/composables/ssr')['useRequestEvent']
  const useRequestFetch: typeof import('../../node_modules/nuxt/dist/app/composables/ssr')['useRequestFetch']
  const useRequestHeader: typeof import('../../node_modules/nuxt/dist/app/composables/ssr')['useRequestHeader']
  const useRequestHeaders: typeof import('../../node_modules/nuxt/dist/app/composables/ssr')['useRequestHeaders']
  const useRequestURL: typeof import('../../node_modules/nuxt/dist/app/composables/url')['useRequestURL']
  const useResizeObserver: typeof import('@vueuse/core')['useResizeObserver']
  const useResponseHeader: typeof import('../../node_modules/nuxt/dist/app/composables/ssr')['useResponseHeader']
  const useRoute: typeof import('../../node_modules/nuxt/dist/app/composables/router')['useRoute']
  const useRouteAnnouncer: typeof import('../../node_modules/nuxt/dist/app/composables/route-announcer')['useRouteAnnouncer']
  const useRouter: typeof import('../../node_modules/nuxt/dist/app/composables/router')['useRouter']
  const useRuntimeConfig: typeof import('../../node_modules/nuxt/dist/app/nuxt')['useRuntimeConfig']
  const useRuntimeHook: typeof import('../../node_modules/nuxt/dist/app/composables/runtime-hook')['useRuntimeHook']
  const useScreenOrientation: typeof import('@vueuse/core')['useScreenOrientation']
  const useScreenSafeArea: typeof import('@vueuse/core')['useScreenSafeArea']
  const useScript: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScript']
  const useScriptClarity: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptClarity']
  const useScriptCloudflareWebAnalytics: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptCloudflareWebAnalytics']
  const useScriptCrisp: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptCrisp']
  const useScriptEventPage: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptEventPage']
  const useScriptFathomAnalytics: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptFathomAnalytics']
  const useScriptGoogleAdsense: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptGoogleAdsense']
  const useScriptGoogleAnalytics: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptGoogleAnalytics']
  const useScriptGoogleMaps: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptGoogleMaps']
  const useScriptGoogleTagManager: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptGoogleTagManager']
  const useScriptHotjar: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptHotjar']
  const useScriptIntercom: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptIntercom']
  const useScriptLemonSqueezy: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptLemonSqueezy']
  const useScriptMatomoAnalytics: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptMatomoAnalytics']
  const useScriptMetaPixel: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptMetaPixel']
  const useScriptNpm: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptNpm']
  const useScriptPlausibleAnalytics: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptPlausibleAnalytics']
  const useScriptSegment: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptSegment']
  const useScriptStripe: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptStripe']
  const useScriptTag: typeof import('@vueuse/core')['useScriptTag']
  const useScriptTriggerConsent: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptTriggerConsent']
  const useScriptTriggerElement: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptTriggerElement']
  const useScriptVimeoPlayer: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptVimeoPlayer']
  const useScriptXPixel: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptXPixel']
  const useScriptYouTubePlayer: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptYouTubePlayer']
  const useScroll: typeof import('@vueuse/core')['useScroll']
  const useScrollLock: typeof import('@vueuse/core')['useScrollLock']
  const useSeoMeta: typeof import('../../node_modules/@unhead/vue')['useSeoMeta']
  const useServerHead: typeof import('../../node_modules/@unhead/vue')['useServerHead']
  const useServerHeadSafe: typeof import('../../node_modules/@unhead/vue')['useServerHeadSafe']
  const useServerSeoMeta: typeof import('../../node_modules/@unhead/vue')['useServerSeoMeta']
  const useSessionStorage: typeof import('@vueuse/core')['useSessionStorage']
  const useShadowRoot: typeof import('../../node_modules/vue')['useShadowRoot']
  const useShare: typeof import('@vueuse/core')['useShare']
  const useSlots: typeof import('../../node_modules/vue')['useSlots']
  const useSorted: typeof import('@vueuse/core')['useSorted']
  const useSpeechRecognition: typeof import('@vueuse/core')['useSpeechRecognition']
  const useSpeechSynthesis: typeof import('@vueuse/core')['useSpeechSynthesis']
  const useState: typeof import('../../node_modules/nuxt/dist/app/composables/state')['useState']
  const useStepper: typeof import('@vueuse/core')['useStepper']
  const useStorageAsync: typeof import('@vueuse/core')['useStorageAsync']
  const useStyleTag: typeof import('@vueuse/core')['useStyleTag']
  const useSubscriptionBilling: typeof import('../../composables/useSubscriptionBilling')['useSubscriptionBilling']
  const useSupported: typeof import('@vueuse/core')['useSupported']
  const useSwipe: typeof import('@vueuse/core')['useSwipe']
  const useSyncQueue: typeof import('../../composables/useSyncQueue')['useSyncQueue']
  const useTaxReporting: typeof import('../../composables/useTaxReporting')['useTaxReporting']
  const useTemplateRef: typeof import('../../node_modules/vue')['useTemplateRef']
  const useTemplateRefsList: typeof import('@vueuse/core')['useTemplateRefsList']
  const useTextDirection: typeof import('@vueuse/core')['useTextDirection']
  const useTextSelection: typeof import('@vueuse/core')['useTextSelection']
  const useTextareaAutosize: typeof import('@vueuse/core')['useTextareaAutosize']
  const useThrottle: typeof import('@vueuse/core')['useThrottle']
  const useThrottleFn: typeof import('@vueuse/core')['useThrottleFn']
  const useThrottledRefHistory: typeof import('@vueuse/core')['useThrottledRefHistory']
  const useTimeAgo: typeof import('@vueuse/core')['useTimeAgo']
  const useTimeout: typeof import('@vueuse/core')['useTimeout']
  const useTimeoutFn: typeof import('@vueuse/core')['useTimeoutFn']
  const useTimeoutPoll: typeof import('@vueuse/core')['useTimeoutPoll']
  const useTimestamp: typeof import('@vueuse/core')['useTimestamp']
  const useToNumber: typeof import('@vueuse/core')['useToNumber']
  const useToString: typeof import('@vueuse/core')['useToString']
  const useToggle: typeof import('@vueuse/core')['useToggle']
  const useTouchGestures: typeof import('../../composables/useTouchGestures')['useTouchGestures']
  const useTransition: typeof import('@vueuse/core')['useTransition']
  const useTransitionState: typeof import('../../node_modules/vue')['useTransitionState']
  const useTransparentPwaIcon: typeof import('../../node_modules/@vite-pwa/nuxt/dist/runtime/composables/index')['useTransparentPwaIcon']
  const useUploads: typeof import('../../composables/uploads')['useUploads']
  const useUrlSearchParams: typeof import('@vueuse/core')['useUrlSearchParams']
  const useUserManagement: typeof import('../../composables/user-management')['useUserManagement']
  const useUserMedia: typeof import('@vueuse/core')['useUserMedia']
  const useUserProfile: typeof import('../../composables/useUserProfile')['useUserProfile']
  const useVModel: typeof import('@vueuse/core')['useVModel']
  const useVModels: typeof import('@vueuse/core')['useVModels']
  const useVibrate: typeof import('@vueuse/core')['useVibrate']
  const useVirtualList: typeof import('@vueuse/core')['useVirtualList']
  const useVisualization: typeof import('../../composables/useVisualization')['useVisualization']
  const useWakeLock: typeof import('@vueuse/core')['useWakeLock']
  const useWebNotification: typeof import('@vueuse/core')['useWebNotification']
  const useWebSocket: typeof import('@vueuse/core')['useWebSocket']
  const useWebWorker: typeof import('@vueuse/core')['useWebWorker']
  const useWebWorkerFn: typeof import('@vueuse/core')['useWebWorkerFn']
  const useWindowFocus: typeof import('@vueuse/core')['useWindowFocus']
  const useWindowScroll: typeof import('@vueuse/core')['useWindowScroll']
  const useWindowSize: typeof import('@vueuse/core')['useWindowSize']
  const useWorkflowTemplates: typeof import('../../composables/useWorkflowTemplates')['useWorkflowTemplates']
  const user: typeof import('../../composables/user')['user']
  const validateDayOfWeek: typeof import('../../composables/useNotificationPreferences')['validateDayOfWeek']
  const validateNotificationFrequency: typeof import('../../composables/useNotificationPreferences')['validateNotificationFrequency']
  const validateNotificationPreferences: typeof import('../../composables/useNotificationPreferences')['validateNotificationPreferences']
  const validateTimeOfDay: typeof import('../../composables/useNotificationPreferences')['validateTimeOfDay']
  const watch: typeof import('../../node_modules/vue')['watch']
  const watchArray: typeof import('@vueuse/core')['watchArray']
  const watchAtMost: typeof import('@vueuse/core')['watchAtMost']
  const watchDebounced: typeof import('@vueuse/core')['watchDebounced']
  const watchDeep: typeof import('@vueuse/core')['watchDeep']
  const watchEffect: typeof import('../../node_modules/vue')['watchEffect']
  const watchIgnorable: typeof import('@vueuse/core')['watchIgnorable']
  const watchImmediate: typeof import('@vueuse/core')['watchImmediate']
  const watchOnce: typeof import('@vueuse/core')['watchOnce']
  const watchPausable: typeof import('@vueuse/core')['watchPausable']
  const watchPostEffect: typeof import('../../node_modules/vue')['watchPostEffect']
  const watchSyncEffect: typeof import('../../node_modules/vue')['watchSyncEffect']
  const watchThrottled: typeof import('@vueuse/core')['watchThrottled']
  const watchTriggerable: typeof import('@vueuse/core')['watchTriggerable']
  const watchWithFilter: typeof import('@vueuse/core')['watchWithFilter']
  const whenever: typeof import('@vueuse/core')['whenever']
  const withCtx: typeof import('../../node_modules/vue')['withCtx']
  const withDirectives: typeof import('../../node_modules/vue')['withDirectives']
  const withKeys: typeof import('../../node_modules/vue')['withKeys']
  const withMemo: typeof import('../../node_modules/vue')['withMemo']
  const withModifiers: typeof import('../../node_modules/vue')['withModifiers']
  const withScopeId: typeof import('../../node_modules/vue')['withScopeId']
}
// for type re-export
declare global {
  // @ts-ignore
  export type { Component, ComponentPublicInstance, ComputedRef, DirectiveBinding, ExtractDefaultPropTypes, ExtractPropTypes, ExtractPublicPropTypes, InjectionKey, PropType, Ref, MaybeRef, MaybeRefOrGetter, VNode, WritableComputedRef } from '../../node_modules/vue'
  import('../../node_modules/vue')
  // @ts-ignore
  export type { meAvatarInterface, MainUserInterface, MainUserSpacesInterface } from '../../composables/me'
  import('../../composables/me')
  // @ts-ignore
  export type { AccountingIntegration, SyncSettings, SyncRecord } from '../../composables/useAccountingIntegration'
  import('../../composables/useAccountingIntegration')
  // @ts-ignore
  export type { AdAnalyticsSummary } from '../../composables/useAdAnalytics'
  import('../../composables/useAdAnalytics')
  // @ts-ignore
  export type { ROIAnalysis, ChannelROI, AudienceROI, CreativeROI } from '../../composables/useAdvertisingROI'
  import('../../composables/useAdvertisingROI')
  // @ts-ignore
  export type { NotificationType, Notification } from '../../composables/useAnalyticsNotifications'
  import('../../composables/useAnalyticsNotifications')
  // @ts-ignore
  export type { RetentionPolicy, RetentionExecution } from '../../composables/useDataRetention'
  import('../../composables/useDataRetention')
  // @ts-ignore
  export type { EmailRetryAttempt, EmailRetryConfig } from '../../composables/useEmailRetry'
  import('../../composables/useEmailRetry')
  // @ts-ignore
  export type { EmailTrackingEvent, EmailMetrics } from '../../composables/useEmailTracking'
  import('../../composables/useEmailTracking')
  // @ts-ignore
  export type { UnsubscribeRecord, EmailPreferences } from '../../composables/useEmailUnsubscribe'
  import('../../composables/useEmailUnsubscribe')
  // @ts-ignore
  export type { ValidationRule, FieldValidation, FormValidation } from '../../composables/useFormValidation'
  import('../../composables/useFormValidation')
  // @ts-ignore
  export type { InvoiceReminder, ReminderTemplate } from '../../composables/useInvoiceReminders'
  import('../../composables/useInvoiceReminders')
  // @ts-ignore
  export type { MLModel, PerformanceMetric, TrainingJob, TrainingConfig, TrainingResults } from '../../composables/useMLPipeline'
  import('../../composables/useMLPipeline')
  // @ts-ignore
  export type { EmailTemplate, LanguageConfig } from '../../composables/useMultiLanguageEmail'
  import('../../composables/useMultiLanguageEmail')
  // @ts-ignore
  export type { NotificationFrequency, NotificationTypePreference, WeeklyReportPreference, NotificationPreferences } from '../../composables/useNotificationPreferences'
  import('../../composables/useNotificationPreferences')
  // @ts-ignore
  export type { NotificationDocument } from '../../composables/useNotificationService'
  import('../../composables/useNotificationService')
  // @ts-ignore
  export type { TaxReport, TaxLineItem, TaxConfiguration, TaxRate, ComplianceSettings } from '../../composables/useTaxReporting'
  import('../../composables/useTaxReporting')
  // @ts-ignore
  export type { UserProfile } from '../../composables/useUserProfile'
  import('../../composables/useUserProfile')
  // @ts-ignore
  export type { WorkflowTemplate } from '../../composables/useWorkflowTemplates'
  import('../../composables/useWorkflowTemplates')
  // @ts-ignore
  export type { WebhookTester, WebhookTestConfig, WebhookTestResult } from '../../utils/webhook-tester'
  import('../../utils/webhook-tester')
}
// for vue template auto import
import { UnwrapRef } from 'vue'
declare module 'vue' {
  interface ComponentCustomProperties {
    readonly DEFAULT_NOTIFICATION_PREFERENCES: UnwrapRef<typeof import('../../composables/useNotificationPreferences')['DEFAULT_NOTIFICATION_PREFERENCES']>
    readonly DEFAULT_ROLE_PERMISSIONS: UnwrapRef<typeof import('../../composables/permissions')['DEFAULT_ROLE_PERMISSIONS']>
    readonly LightenDarkenColorHex: UnwrapRef<typeof import('../../composables/info')['LightenDarkenColorHex']>
    readonly PERMISSIONS: UnwrapRef<typeof import('../../composables/permissions')['PERMISSIONS']>
    readonly PERMISSION_CATEGORIES: UnwrapRef<typeof import('../../composables/permissions')['PERMISSION_CATEGORIES']>
    readonly RGBToHex: UnwrapRef<typeof import('../../composables/info')['RGBToHex']>
    readonly WebhookTester: UnwrapRef<typeof import('../../utils/webhook-tester')['WebhookTester']>
    readonly abortNavigation: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/router')['abortNavigation']>
    readonly add: UnwrapRef<typeof import('../../composables/firebase')['add']>
    readonly addFcmToken: UnwrapRef<typeof import('../../composables/firebase')['addFcmToken']>
    readonly addRouteMiddleware: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/router')['addRouteMiddleware']>
    readonly alphanumeric: UnwrapRef<typeof import('../../composables/validationRules')['alphanumeric']>
    readonly arrayUniqueValue: UnwrapRef<typeof import('../../composables/info')['arrayUniqueValue']>
    readonly arrayUniqueValueOfArray: UnwrapRef<typeof import('../../composables/info')['arrayUniqueValueOfArray']>
    readonly array_move: UnwrapRef<typeof import('../../composables/info')['array_move']>
    readonly asyncComputed: UnwrapRef<typeof import('@vueuse/core')['asyncComputed']>
    readonly autoResetRef: UnwrapRef<typeof import('@vueuse/core')['autoResetRef']>
    readonly callOnce: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/once')['callOnce']>
    readonly cancelIdleCallback: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/compat/idle-callback')['cancelIdleCallback']>
    readonly changeCurrentSpace: UnwrapRef<typeof import('../../composables/space')['changeCurrentSpace']>
    readonly changecurrentClient: UnwrapRef<typeof import('../../composables/user')['changecurrentClient']>
    readonly changecurrentUser: UnwrapRef<typeof import('../../composables/user')['changecurrentUser']>
    readonly checkIfContact: UnwrapRef<typeof import('../../composables/user')['checkIfContact']>
    readonly checkIfUser: UnwrapRef<typeof import('../../composables/user')['checkIfUser']>
    readonly checkNotificationsEnabled: UnwrapRef<typeof import('../../composables/firebase')['checkNotificationsEnabled']>
    readonly clearCurrentSpace: UnwrapRef<typeof import('../../composables/space')['clearCurrentSpace']>
    readonly clearError: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/error')['clearError']>
    readonly clearNuxtData: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/asyncData')['clearNuxtData']>
    readonly clearNuxtState: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/state')['clearNuxtState']>
    readonly clearcurrentClient: UnwrapRef<typeof import('../../composables/user')['clearcurrentClient']>
    readonly clearcurrentUser: UnwrapRef<typeof import('../../composables/user')['clearcurrentUser']>
    readonly computed: UnwrapRef<typeof import('../../node_modules/vue')['computed']>
    readonly computedAsync: UnwrapRef<typeof import('@vueuse/core')['computedAsync']>
    readonly computedEager: UnwrapRef<typeof import('@vueuse/core')['computedEager']>
    readonly computedInject: UnwrapRef<typeof import('@vueuse/core')['computedInject']>
    readonly computedWithControl: UnwrapRef<typeof import('@vueuse/core')['computedWithControl']>
    readonly controlledComputed: UnwrapRef<typeof import('@vueuse/core')['controlledComputed']>
    readonly controlledRef: UnwrapRef<typeof import('@vueuse/core')['controlledRef']>
    readonly createElementFromHTML: UnwrapRef<typeof import('../../composables/info')['createElementFromHTML']>
    readonly createEmail: UnwrapRef<typeof import('../../composables/email')['createEmail']>
    readonly createError: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/error')['createError']>
    readonly createEventHook: UnwrapRef<typeof import('@vueuse/core')['createEventHook']>
    readonly createGlobalState: UnwrapRef<typeof import('@vueuse/core')['createGlobalState']>
    readonly createInjectionState: UnwrapRef<typeof import('@vueuse/core')['createInjectionState']>
    readonly createReactiveFn: UnwrapRef<typeof import('@vueuse/core')['createReactiveFn']>
    readonly createReusableTemplate: UnwrapRef<typeof import('@vueuse/core')['createReusableTemplate']>
    readonly createSharedComposable: UnwrapRef<typeof import('@vueuse/core')['createSharedComposable']>
    readonly createTemplatePromise: UnwrapRef<typeof import('@vueuse/core')['createTemplatePromise']>
    readonly createUnrefFn: UnwrapRef<typeof import('@vueuse/core')['createUnrefFn']>
    readonly createWebhookTester: UnwrapRef<typeof import('../../utils/webhook-tester')['createWebhookTester']>
    readonly customRef: UnwrapRef<typeof import('../../node_modules/vue')['customRef']>
    readonly database: UnwrapRef<typeof import('../../composables/database')['database']>
    readonly daysRemaining: UnwrapRef<typeof import('../../composables/info')['daysRemaining']>
    readonly debouncedRef: UnwrapRef<typeof import('@vueuse/core')['debouncedRef']>
    readonly debouncedWatch: UnwrapRef<typeof import('@vueuse/core')['debouncedWatch']>
    readonly defineAppConfig: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/nuxt')['defineAppConfig']>
    readonly defineAsyncComponent: UnwrapRef<typeof import('../../node_modules/vue')['defineAsyncComponent']>
    readonly defineComponent: UnwrapRef<typeof import('../../node_modules/vue')['defineComponent']>
    readonly defineNuxtComponent: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/component')['defineNuxtComponent']>
    readonly defineNuxtLink: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/components/nuxt-link')['defineNuxtLink']>
    readonly defineNuxtPlugin: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/nuxt')['defineNuxtPlugin']>
    readonly defineNuxtRouteMiddleware: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/router')['defineNuxtRouteMiddleware']>
    readonly definePageMeta: UnwrapRef<typeof import('../../node_modules/nuxt/dist/pages/runtime/composables')['definePageMeta']>
    readonly definePayloadPlugin: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/nuxt')['definePayloadPlugin']>
    readonly definePayloadReducer: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/payload')['definePayloadReducer']>
    readonly definePayloadReviver: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/payload')['definePayloadReviver']>
    readonly del: UnwrapRef<typeof import('../../composables/firebase')['del']>
    readonly deleteBatch: UnwrapRef<typeof import('../../composables/firebase')['deleteBatch']>
    readonly deleteById: UnwrapRef<typeof import('../../composables/firebase')['deleteById']>
    readonly deleteItems: UnwrapRef<typeof import('../../composables/info')['deleteItems']>
    readonly eagerComputed: UnwrapRef<typeof import('@vueuse/core')['eagerComputed']>
    readonly effect: UnwrapRef<typeof import('../../node_modules/vue')['effect']>
    readonly effectScope: UnwrapRef<typeof import('../../node_modules/vue')['effectScope']>
    readonly email: UnwrapRef<typeof import('../../composables/validationRules')['email']>
    readonly extendRef: UnwrapRef<typeof import('@vueuse/core')['extendRef']>
    readonly extraInfo: UnwrapRef<typeof import('../../composables/info')['extraInfo']>
    readonly extraInfoUpdate: UnwrapRef<typeof import('../../composables/info')['extraInfoUpdate']>
    readonly extraUpdateInfo: UnwrapRef<typeof import('../../composables/info')['extraUpdateInfo']>
    readonly formatPhoneNumber: UnwrapRef<typeof import('../../composables/info')['formatPhoneNumber']>
    readonly formatPhoneNumberPlus: UnwrapRef<typeof import('../../composables/info')['formatPhoneNumberPlus']>
    readonly formatter: UnwrapRef<typeof import('../../composables/info')['formatter']>
    readonly getAccess: UnwrapRef<typeof import('../../composables/user')['getAccess']>
    readonly getAccounts: UnwrapRef<typeof import('../../composables/user')['getAccounts']>
    readonly getAppManifest: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/manifest')['getAppManifest']>
    readonly getClientBlock: UnwrapRef<typeof import('../../composables/user')['getClientBlock']>
    readonly getCountry: UnwrapRef<typeof import('../../composables/info')['getCountry']>
    readonly getCurrency: UnwrapRef<typeof import('../../composables/info')['getCurrency']>
    readonly getCurrentInstance: UnwrapRef<typeof import('../../node_modules/vue')['getCurrentInstance']>
    readonly getCurrentScope: UnwrapRef<typeof import('../../node_modules/vue')['getCurrentScope']>
    readonly getGmail: UnwrapRef<typeof import('../../composables/user')['getGmail']>
    readonly getIndustry: UnwrapRef<typeof import('../../composables/info')['getIndustry']>
    readonly getIp: UnwrapRef<typeof import('../../composables/info')['getIp']>
    readonly getMeta: UnwrapRef<typeof import('../../composables/info')['getMeta']>
    readonly getRandomInt: UnwrapRef<typeof import('../../composables/info')['getRandomInt']>
    readonly getRouteRules: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/manifest')['getRouteRules']>
    readonly getSingleStats: UnwrapRef<typeof import('../../composables/stats')['getSingleStats']>
    readonly getSpaceStats: UnwrapRef<typeof import('../../composables/stats')['getSpaceStats']>
    readonly getSrc: UnwrapRef<typeof import('../../composables/info')['getSrc']>
    readonly getState: UnwrapRef<typeof import('../../composables/info')['getState']>
    readonly getStatsBusiness: UnwrapRef<typeof import('../../composables/info')['getStatsBusiness']>
    readonly getStatsCustomer: UnwrapRef<typeof import('../../composables/info')['getStatsCustomer']>
    readonly getTimeZone: UnwrapRef<typeof import('../../composables/info')['getTimeZone']>
    readonly getUserStats: UnwrapRef<typeof import('../../composables/stats')['getUserStats']>
    readonly getWorkspaceType: UnwrapRef<typeof import('../../composables/info')['getWorkspaceType']>
    readonly h: UnwrapRef<typeof import('../../node_modules/vue')['h']>
    readonly hasInjectionContext: UnwrapRef<typeof import('../../node_modules/vue')['hasInjectionContext']>
    readonly hasRole: UnwrapRef<typeof import('../../composables/info')['hasRole']>
    readonly hexToHSL: UnwrapRef<typeof import('../../composables/info')['hexToHSL']>
    readonly hexToRGBA: UnwrapRef<typeof import('../../composables/info')['hexToRGBA']>
    readonly hexToRgbA: UnwrapRef<typeof import('../../composables/info')['hexToRgbA']>
    readonly htmlToElement: UnwrapRef<typeof import('../../composables/info')['htmlToElement']>
    readonly htmlToElements: UnwrapRef<typeof import('../../composables/info')['htmlToElements']>
    readonly ignorableWatch: UnwrapRef<typeof import('@vueuse/core')['ignorableWatch']>
    readonly incrementValue: UnwrapRef<typeof import('../../composables/firebase')['incrementValue']>
    readonly inject: UnwrapRef<typeof import('../../node_modules/vue')['inject']>
    readonly injectHead: UnwrapRef<typeof import('../../node_modules/@unhead/vue')['injectHead']>
    readonly injectLocal: UnwrapRef<typeof import('@vueuse/core')['injectLocal']>
    readonly isAdmin: UnwrapRef<typeof import('../../composables/info')['isAdmin']>
    readonly isDefined: UnwrapRef<typeof import('@vueuse/core')['isDefined']>
    readonly isNuxtError: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/error')['isNuxtError']>
    readonly isPrerendered: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/payload')['isPrerendered']>
    readonly isProxy: UnwrapRef<typeof import('../../node_modules/vue')['isProxy']>
    readonly isReactive: UnwrapRef<typeof import('../../node_modules/vue')['isReactive']>
    readonly isReadonly: UnwrapRef<typeof import('../../node_modules/vue')['isReadonly']>
    readonly isRef: UnwrapRef<typeof import('../../node_modules/vue')['isRef']>
    readonly isShallow: UnwrapRef<typeof import('../../node_modules/vue')['isShallow']>
    readonly isSuperAdmin: UnwrapRef<typeof import('../../composables/info')['isSuperAdmin']>
    readonly isVue2: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/compat/vue-demi')['isVue2']>
    readonly isVue3: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/compat/vue-demi')['isVue3']>
    readonly lightBackgroundDarkTextRandom: UnwrapRef<typeof import('../../composables/info')['lightBackgroundDarkTextRandom']>
    readonly loadPayload: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/payload')['loadPayload']>
    readonly logBatteryInfo: UnwrapRef<typeof import('../../composables/info')['logBatteryInfo']>
    readonly loginUser: UnwrapRef<typeof import('../../composables/firebase')['loginUser']>
    readonly logoutUser: UnwrapRef<typeof import('../../composables/firebase')['logoutUser']>
    readonly makeDateTimeReadable: UnwrapRef<typeof import('../../composables/info')['makeDateTimeReadable']>
    readonly makeDateTimeUserReadable: UnwrapRef<typeof import('../../composables/info')['makeDateTimeUserReadable']>
    readonly makeDestructurable: UnwrapRef<typeof import('@vueuse/core')['makeDestructurable']>
    readonly makeid: UnwrapRef<typeof import('../../composables/info')['makeid']>
    readonly markRaw: UnwrapRef<typeof import('../../node_modules/vue')['markRaw']>
    readonly max: UnwrapRef<typeof import('../../composables/validationRules')['max']>
    readonly maxLength: UnwrapRef<typeof import('../../composables/validationRules')['maxLength']>
    readonly mergeModels: UnwrapRef<typeof import('../../node_modules/vue')['mergeModels']>
    readonly min: UnwrapRef<typeof import('../../composables/validationRules')['min']>
    readonly minLength: UnwrapRef<typeof import('../../composables/validationRules')['minLength']>
    readonly navigateTo: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/router')['navigateTo']>
    readonly networkstatus: UnwrapRef<typeof import('../../composables/networkstatus')['default']>
    readonly nextTick: UnwrapRef<typeof import('../../node_modules/vue')['nextTick']>
    readonly notifications: UnwrapRef<typeof import('../../composables/notifications')['default']>
    readonly numberToMonth: UnwrapRef<typeof import('../../composables/info')['numberToMonth']>
    readonly numberToShortMonth: UnwrapRef<typeof import('../../composables/info')['numberToShortMonth']>
    readonly numeric: UnwrapRef<typeof import('../../composables/validationRules')['numeric']>
    readonly onActivated: UnwrapRef<typeof import('../../node_modules/vue')['onActivated']>
    readonly onBeforeMount: UnwrapRef<typeof import('../../node_modules/vue')['onBeforeMount']>
    readonly onBeforeRouteLeave: UnwrapRef<typeof import('../../node_modules/vue-router')['onBeforeRouteLeave']>
    readonly onBeforeRouteUpdate: UnwrapRef<typeof import('../../node_modules/vue-router')['onBeforeRouteUpdate']>
    readonly onBeforeUnmount: UnwrapRef<typeof import('../../node_modules/vue')['onBeforeUnmount']>
    readonly onBeforeUpdate: UnwrapRef<typeof import('../../node_modules/vue')['onBeforeUpdate']>
    readonly onClickOutside: UnwrapRef<typeof import('@vueuse/core')['onClickOutside']>
    readonly onDeactivated: UnwrapRef<typeof import('../../node_modules/vue')['onDeactivated']>
    readonly onErrorCaptured: UnwrapRef<typeof import('../../node_modules/vue')['onErrorCaptured']>
    readonly onKeyStroke: UnwrapRef<typeof import('@vueuse/core')['onKeyStroke']>
    readonly onLongPress: UnwrapRef<typeof import('@vueuse/core')['onLongPress']>
    readonly onMounted: UnwrapRef<typeof import('../../node_modules/vue')['onMounted']>
    readonly onNuxtReady: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/ready')['onNuxtReady']>
    readonly onPrehydrate: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/ssr')['onPrehydrate']>
    readonly onRenderTracked: UnwrapRef<typeof import('../../node_modules/vue')['onRenderTracked']>
    readonly onRenderTriggered: UnwrapRef<typeof import('../../node_modules/vue')['onRenderTriggered']>
    readonly onScopeDispose: UnwrapRef<typeof import('../../node_modules/vue')['onScopeDispose']>
    readonly onServerPrefetch: UnwrapRef<typeof import('../../node_modules/vue')['onServerPrefetch']>
    readonly onStartTyping: UnwrapRef<typeof import('@vueuse/core')['onStartTyping']>
    readonly onUnmounted: UnwrapRef<typeof import('../../node_modules/vue')['onUnmounted']>
    readonly onUpdated: UnwrapRef<typeof import('../../node_modules/vue')['onUpdated']>
    readonly pattern: UnwrapRef<typeof import('../../composables/validationRules')['pattern']>
    readonly pausableWatch: UnwrapRef<typeof import('@vueuse/core')['pausableWatch']>
    readonly phone: UnwrapRef<typeof import('../../composables/validationRules')['phone']>
    readonly prefetchComponents: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/preload')['prefetchComponents']>
    readonly preloadComponents: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/preload')['preloadComponents']>
    readonly preloadPayload: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/payload')['preloadPayload']>
    readonly preloadRouteComponents: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/preload')['preloadRouteComponents']>
    readonly prerenderRoutes: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/ssr')['prerenderRoutes']>
    readonly provide: UnwrapRef<typeof import('../../node_modules/vue')['provide']>
    readonly provideLocal: UnwrapRef<typeof import('@vueuse/core')['provideLocal']>
    readonly proxyRefs: UnwrapRef<typeof import('../../node_modules/vue')['proxyRefs']>
    readonly queryByCollection: UnwrapRef<typeof import('../../composables/firebase')['queryByCollection']>
    readonly queryByCollectionLimit: UnwrapRef<typeof import('../../composables/firebase')['queryByCollectionLimit']>
    readonly queryById: UnwrapRef<typeof import('../../composables/firebase')['queryById']>
    readonly queryByWhere2: UnwrapRef<typeof import('../../composables/firebase')['queryByWhere2']>
    readonly queryByWhere: UnwrapRef<typeof import('../../composables/firebase')['queryByWhere']>
    readonly queryByWhereDouble: UnwrapRef<typeof import('../../composables/firebase')['queryByWhereDouble']>
    readonly queryByWhereDoubleLimit: UnwrapRef<typeof import('../../composables/firebase')['queryByWhereDoubleLimit']>
    readonly queryByWhereFar: UnwrapRef<typeof import('../../composables/firebase')['queryByWhereFar']>
    readonly queryByWhereLimit: UnwrapRef<typeof import('../../composables/firebase')['queryByWhereLimit']>
    readonly queryByWhereTrippleLimit: UnwrapRef<typeof import('../../composables/firebase')['queryByWhereTrippleLimit']>
    readonly randomBotUrl: UnwrapRef<typeof import('../../composables/info')['randomBotUrl']>
    readonly randomColor: UnwrapRef<typeof import('../../composables/info')['randomColor']>
    readonly randomColorName: UnwrapRef<typeof import('../../composables/info')['randomColorName']>
    readonly randomItemInArray: UnwrapRef<typeof import('../../composables/info')['randomItemInArray']>
    readonly randomPerson: UnwrapRef<typeof import('../../composables/info')['randomPerson']>
    readonly randomUnoColor: UnwrapRef<typeof import('../../composables/info')['randomUnoColor']>
    readonly reactify: UnwrapRef<typeof import('@vueuse/core')['reactify']>
    readonly reactifyObject: UnwrapRef<typeof import('@vueuse/core')['reactifyObject']>
    readonly reactive: UnwrapRef<typeof import('../../node_modules/vue')['reactive']>
    readonly reactiveComputed: UnwrapRef<typeof import('@vueuse/core')['reactiveComputed']>
    readonly reactiveOmit: UnwrapRef<typeof import('@vueuse/core')['reactiveOmit']>
    readonly reactivePick: UnwrapRef<typeof import('@vueuse/core')['reactivePick']>
    readonly readonly: UnwrapRef<typeof import('../../node_modules/vue')['readonly']>
    readonly ref: UnwrapRef<typeof import('../../node_modules/vue')['ref']>
    readonly refAutoReset: UnwrapRef<typeof import('@vueuse/core')['refAutoReset']>
    readonly refDebounced: UnwrapRef<typeof import('@vueuse/core')['refDebounced']>
    readonly refDefault: UnwrapRef<typeof import('@vueuse/core')['refDefault']>
    readonly refThrottled: UnwrapRef<typeof import('@vueuse/core')['refThrottled']>
    readonly refWithControl: UnwrapRef<typeof import('@vueuse/core')['refWithControl']>
    readonly refreshCookie: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/cookie')['refreshCookie']>
    readonly refreshNuxtData: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/asyncData')['refreshNuxtData']>
    readonly registerUser: UnwrapRef<typeof import('../../composables/firebase')['registerUser']>
    readonly reloadNuxtApp: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/chunk')['reloadNuxtApp']>
    readonly removeExtra: UnwrapRef<typeof import('../../composables/info')['removeExtra']>
    readonly removeUndefined: UnwrapRef<typeof import('../../composables/info')['removeUndefined']>
    readonly requestIdleCallback: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/compat/idle-callback')['requestIdleCallback']>
    readonly required: UnwrapRef<typeof import('../../composables/validationRules')['required']>
    readonly resolveComponent: UnwrapRef<typeof import('../../node_modules/vue')['resolveComponent']>
    readonly resolveRef: UnwrapRef<typeof import('@vueuse/core')['resolveRef']>
    readonly resolveUnref: UnwrapRef<typeof import('@vueuse/core')['resolveUnref']>
    readonly runAllWebhookTests: UnwrapRef<typeof import('../../utils/webhook-tester')['runAllWebhookTests']>
    readonly schemaHeaders: UnwrapRef<typeof import('../../composables/forms')['schemaHeaders']>
    readonly schemaSkin: UnwrapRef<typeof import('../../composables/forms')['schemaSkin']>
    readonly sendEmail: UnwrapRef<typeof import('../../composables/email')['sendEmail']>
    readonly set: UnwrapRef<typeof import('../../composables/firebase')['set']>
    readonly setBatch: UnwrapRef<typeof import('../../composables/firebase')['setBatch']>
    readonly setBatchMore: UnwrapRef<typeof import('../../composables/firebase')['setBatchMore']>
    readonly setById: UnwrapRef<typeof import('../../composables/firebase')['setById']>
    readonly setInterval: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/compat/interval')['setInterval']>
    readonly setPageLayout: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/router')['setPageLayout']>
    readonly setResponseStatus: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/ssr')['setResponseStatus']>
    readonly setcurrentClient: UnwrapRef<typeof import('../../composables/user')['setcurrentClient']>
    readonly shallowReactive: UnwrapRef<typeof import('../../node_modules/vue')['shallowReactive']>
    readonly shallowReadonly: UnwrapRef<typeof import('../../node_modules/vue')['shallowReadonly']>
    readonly shallowRef: UnwrapRef<typeof import('../../node_modules/vue')['shallowRef']>
    readonly showError: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/error')['showError']>
    readonly smallerArray: UnwrapRef<typeof import('../../composables/info')['smallerArray']>
    readonly sortableAB: UnwrapRef<typeof import('../../composables/info')['sortableAB']>
    readonly sortableAlgo: UnwrapRef<typeof import('../../composables/info')['sortableAlgo']>
    readonly sortableBA: UnwrapRef<typeof import('../../composables/info')['sortableBA']>
    readonly space: UnwrapRef<typeof import('../../composables/space')['space']>
    readonly stats: UnwrapRef<typeof import('../../composables/stats')['stats']>
    readonly syncRef: UnwrapRef<typeof import('@vueuse/core')['syncRef']>
    readonly syncRefs: UnwrapRef<typeof import('@vueuse/core')['syncRefs']>
    readonly templateRef: UnwrapRef<typeof import('@vueuse/core')['templateRef']>
    readonly thousandSeparated: UnwrapRef<typeof import('../../composables/info')['thousandSeparated']>
    readonly thousandSeparatedToFixed: UnwrapRef<typeof import('../../composables/info')['thousandSeparatedToFixed']>
    readonly throttledRef: UnwrapRef<typeof import('@vueuse/core')['throttledRef']>
    readonly throttledWatch: UnwrapRef<typeof import('@vueuse/core')['throttledWatch']>
    readonly toRaw: UnwrapRef<typeof import('../../node_modules/vue')['toRaw']>
    readonly toReactive: UnwrapRef<typeof import('@vueuse/core')['toReactive']>
    readonly toRef: UnwrapRef<typeof import('../../node_modules/vue')['toRef']>
    readonly toRefs: UnwrapRef<typeof import('../../node_modules/vue')['toRefs']>
    readonly toValue: UnwrapRef<typeof import('../../node_modules/vue')['toValue']>
    readonly toggleUserStatus: UnwrapRef<typeof import('../../composables/firebase')['toggleUserStatus']>
    readonly triggerRef: UnwrapRef<typeof import('../../node_modules/vue')['triggerRef']>
    readonly tryOnBeforeMount: UnwrapRef<typeof import('@vueuse/core')['tryOnBeforeMount']>
    readonly tryOnBeforeUnmount: UnwrapRef<typeof import('@vueuse/core')['tryOnBeforeUnmount']>
    readonly tryOnMounted: UnwrapRef<typeof import('@vueuse/core')['tryOnMounted']>
    readonly tryOnScopeDispose: UnwrapRef<typeof import('@vueuse/core')['tryOnScopeDispose']>
    readonly tryOnUnmounted: UnwrapRef<typeof import('@vueuse/core')['tryOnUnmounted']>
    readonly tryUseNuxtApp: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/nuxt')['tryUseNuxtApp']>
    readonly unref: UnwrapRef<typeof import('../../node_modules/vue')['unref']>
    readonly unrefElement: UnwrapRef<typeof import('@vueuse/core')['unrefElement']>
    readonly until: UnwrapRef<typeof import('@vueuse/core')['until']>
    readonly update: UnwrapRef<typeof import('../../composables/firebase')['update']>
    readonly updateAppConfig: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/config')['updateAppConfig']>
    readonly updateBatch: UnwrapRef<typeof import('../../composables/firebase')['updateBatch']>
    readonly updateCurrentSpace: UnwrapRef<typeof import('../../composables/space')['updateCurrentSpace']>
    readonly updateRoot: UnwrapRef<typeof import('../../composables/info')['updateRoot']>
    readonly updateUserRole: UnwrapRef<typeof import('../../composables/firebase')['updateUserRole']>
    readonly updatecurrentClient: UnwrapRef<typeof import('../../composables/user')['updatecurrentClient']>
    readonly updatecurrentUser: UnwrapRef<typeof import('../../composables/user')['updatecurrentUser']>
    readonly upload: UnwrapRef<typeof import('../../composables/firebase')['upload']>
    readonly url: UnwrapRef<typeof import('../../composables/validationRules')['url']>
    readonly useABTesting: UnwrapRef<typeof import('../../composables/useABTesting')['useABTesting']>
    readonly useAccountingIntegration: UnwrapRef<typeof import('../../composables/useAccountingIntegration')['useAccountingIntegration']>
    readonly useActiveElement: UnwrapRef<typeof import('@vueuse/core')['useActiveElement']>
    readonly useActivityTracking: UnwrapRef<typeof import('../../composables/activity-tracking')['useActivityTracking']>
    readonly useAdAnalytics: UnwrapRef<typeof import('../../composables/useAdAnalytics')['useAdAnalytics']>
    readonly useAdContent: UnwrapRef<typeof import('../../composables/useAdContent')['useAdContent']>
    readonly useAdInvoices: UnwrapRef<typeof import('../../composables/useAdInvoices')['useAdInvoices']>
    readonly useAdPayments: UnwrapRef<typeof import('../../composables/useAdPayments')['useAdPayments']>
    readonly useAdSpotContent: UnwrapRef<typeof import('../../composables/useAdSpotContent')['useAdSpotContent']>
    readonly useAdSpots: UnwrapRef<typeof import('../../composables/useAdSpots')['useAdSpots']>
    readonly useAdSubscriptions: UnwrapRef<typeof import('../../composables/useAdSubscriptions')['useAdSubscriptions']>
    readonly useAdminAnalytics: UnwrapRef<typeof import('../../composables/useAdminAnalytics')['useAdminAnalytics']>
    readonly useAdminDashboard: UnwrapRef<typeof import('../../composables/admin-dashboard')['useAdminDashboard']>
    readonly useAdvertisingROI: UnwrapRef<typeof import('../../composables/useAdvertisingROI')['useAdvertisingROI']>
    readonly useAnalytics: UnwrapRef<typeof import('../../composables/useAnalytics')['useAnalytics']>
    readonly useAnalyticsNotifications: UnwrapRef<typeof import('../../composables/useAnalyticsNotifications')['useAnalyticsNotifications']>
    readonly useAnalyticsTracking: UnwrapRef<typeof import('../../composables/analytics-tracking')['useAnalyticsTracking']>
    readonly useAnimate: UnwrapRef<typeof import('@vueuse/core')['useAnimate']>
    readonly useAppConfig: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/config')['useAppConfig']>
    readonly useApplePwaIcon: UnwrapRef<typeof import('../../node_modules/@vite-pwa/nuxt/dist/runtime/composables/index')['useApplePwaIcon']>
    readonly useAppleSplashScreenPwaIcon: UnwrapRef<typeof import('../../node_modules/@vite-pwa/nuxt/dist/runtime/composables/index')['useAppleSplashScreenPwaIcon']>
    readonly useArrayDifference: UnwrapRef<typeof import('@vueuse/core')['useArrayDifference']>
    readonly useArrayEvery: UnwrapRef<typeof import('@vueuse/core')['useArrayEvery']>
    readonly useArrayFilter: UnwrapRef<typeof import('@vueuse/core')['useArrayFilter']>
    readonly useArrayFind: UnwrapRef<typeof import('@vueuse/core')['useArrayFind']>
    readonly useArrayFindIndex: UnwrapRef<typeof import('@vueuse/core')['useArrayFindIndex']>
    readonly useArrayFindLast: UnwrapRef<typeof import('@vueuse/core')['useArrayFindLast']>
    readonly useArrayIncludes: UnwrapRef<typeof import('@vueuse/core')['useArrayIncludes']>
    readonly useArrayJoin: UnwrapRef<typeof import('@vueuse/core')['useArrayJoin']>
    readonly useArrayMap: UnwrapRef<typeof import('@vueuse/core')['useArrayMap']>
    readonly useArrayReduce: UnwrapRef<typeof import('@vueuse/core')['useArrayReduce']>
    readonly useArraySome: UnwrapRef<typeof import('@vueuse/core')['useArraySome']>
    readonly useArrayUnique: UnwrapRef<typeof import('@vueuse/core')['useArrayUnique']>
    readonly useAsyncData: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/asyncData')['useAsyncData']>
    readonly useAsyncQueue: UnwrapRef<typeof import('@vueuse/core')['useAsyncQueue']>
    readonly useAsyncState: UnwrapRef<typeof import('@vueuse/core')['useAsyncState']>
    readonly useAttrs: UnwrapRef<typeof import('../../node_modules/vue')['useAttrs']>
    readonly useAuth: UnwrapRef<typeof import('../../composables/useAuth')['useAuth']>
    readonly useAuthUtils: UnwrapRef<typeof import('../../composables/auth-utils')['useAuthUtils']>
    readonly useAutomatedInvoicing: UnwrapRef<typeof import('../../composables/useAutomatedInvoicing')['useAutomatedInvoicing']>
    readonly useBackgroundSync: UnwrapRef<typeof import('../../composables/useBackgroundSync')['useBackgroundSync']>
    readonly useBase64: UnwrapRef<typeof import('@vueuse/core')['useBase64']>
    readonly useBattery: UnwrapRef<typeof import('@vueuse/core')['useBattery']>
    readonly useBlogs: UnwrapRef<typeof import('../../composables/blog')['useBlogs']>
    readonly useBluetooth: UnwrapRef<typeof import('@vueuse/core')['useBluetooth']>
    readonly useBreakpoints: UnwrapRef<typeof import('@vueuse/core')['useBreakpoints']>
    readonly useBroadcastChannel: UnwrapRef<typeof import('@vueuse/core')['useBroadcastChannel']>
    readonly useBrowserLocation: UnwrapRef<typeof import('@vueuse/core')['useBrowserLocation']>
    readonly useBrowserUrl: UnwrapRef<typeof import('../../composables/info')['useBrowserUrl']>
    readonly useBuilderComponents: UnwrapRef<typeof import('../../composables/builder-components')['useBuilderComponents']>
    readonly useCRM: UnwrapRef<typeof import('../../composables/crm')['useCRM']>
    readonly useCacheManager: UnwrapRef<typeof import('../../composables/useCacheManager')['useCacheManager']>
    readonly useCacheOptimizer: UnwrapRef<typeof import('../../composables/useCacheOptimizer')['useCacheOptimizer']>
    readonly useCached: UnwrapRef<typeof import('@vueuse/core')['useCached']>
    readonly useCashFlowAnalytics: UnwrapRef<typeof import('../../composables/useCashFlowAnalytics')['useCashFlowAnalytics']>
    readonly useClipboard: UnwrapRef<typeof import('@vueuse/core')['useClipboard']>
    readonly useClipboardItems: UnwrapRef<typeof import('@vueuse/core')['useClipboardItems']>
    readonly useCloned: UnwrapRef<typeof import('@vueuse/core')['useCloned']>
    readonly useColorMode: UnwrapRef<typeof import('@vueuse/core')['useColorMode']>
    readonly useConfirmDialog: UnwrapRef<typeof import('@vueuse/core')['useConfirmDialog']>
    readonly useContent: UnwrapRef<typeof import('../../composables/content')['useContent']>
    readonly useContentModeration: UnwrapRef<typeof import('../../composables/content-moderation')['useContentModeration']>
    readonly useCookie: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/cookie')['useCookie']>
    readonly useCounter: UnwrapRef<typeof import('@vueuse/core')['useCounter']>
    readonly useCssModule: UnwrapRef<typeof import('../../node_modules/vue')['useCssModule']>
    readonly useCssVar: UnwrapRef<typeof import('@vueuse/core')['useCssVar']>
    readonly useCssVars: UnwrapRef<typeof import('../../node_modules/vue')['useCssVars']>
    readonly useCurrentElement: UnwrapRef<typeof import('@vueuse/core')['useCurrentElement']>
    readonly useCurrentUser: UnwrapRef<typeof import('../../composables/useCurrentUser')['useCurrentUser']>
    readonly useCycleList: UnwrapRef<typeof import('@vueuse/core')['useCycleList']>
    readonly useDark: UnwrapRef<typeof import('@vueuse/core')['useDark']>
    readonly useDataRetention: UnwrapRef<typeof import('../../composables/useDataRetention')['useDataRetention']>
    readonly useDateFormat: UnwrapRef<typeof import('@vueuse/core')['useDateFormat']>
    readonly useDebounce: UnwrapRef<typeof import('@vueuse/core')['useDebounce']>
    readonly useDebounceFn: UnwrapRef<typeof import('@vueuse/core')['useDebounceFn']>
    readonly useDebouncedRefHistory: UnwrapRef<typeof import('@vueuse/core')['useDebouncedRefHistory']>
    readonly useDeviceMotion: UnwrapRef<typeof import('@vueuse/core')['useDeviceMotion']>
    readonly useDeviceOrientation: UnwrapRef<typeof import('@vueuse/core')['useDeviceOrientation']>
    readonly useDevicePixelRatio: UnwrapRef<typeof import('@vueuse/core')['useDevicePixelRatio']>
    readonly useDevicesList: UnwrapRef<typeof import('@vueuse/core')['useDevicesList']>
    readonly useDisplayMedia: UnwrapRef<typeof import('@vueuse/core')['useDisplayMedia']>
    readonly useDocumentVisibility: UnwrapRef<typeof import('@vueuse/core')['useDocumentVisibility']>
    readonly useDraggable: UnwrapRef<typeof import('@vueuse/core')['useDraggable']>
    readonly useDropZone: UnwrapRef<typeof import('@vueuse/core')['useDropZone']>
    readonly useElementBounding: UnwrapRef<typeof import('@vueuse/core')['useElementBounding']>
    readonly useElementByPoint: UnwrapRef<typeof import('@vueuse/core')['useElementByPoint']>
    readonly useElementHover: UnwrapRef<typeof import('@vueuse/core')['useElementHover']>
    readonly useElementSize: UnwrapRef<typeof import('@vueuse/core')['useElementSize']>
    readonly useElementVisibility: UnwrapRef<typeof import('@vueuse/core')['useElementVisibility']>
    readonly useEmail: UnwrapRef<typeof import('../../composables/useEmail')['useEmail']>
    readonly useEmailRetry: UnwrapRef<typeof import('../../composables/useEmailRetry')['useEmailRetry']>
    readonly useEmailTracking: UnwrapRef<typeof import('../../composables/useEmailTracking')['useEmailTracking']>
    readonly useEmailUnsubscribe: UnwrapRef<typeof import('../../composables/useEmailUnsubscribe')['useEmailUnsubscribe']>
    readonly useEngagementFunnel: UnwrapRef<typeof import('../../composables/useEngagementFunnel')['useEngagementFunnel']>
    readonly useError: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/error')['useError']>
    readonly useEventBus: UnwrapRef<typeof import('@vueuse/core')['useEventBus']>
    readonly useEventListener: UnwrapRef<typeof import('@vueuse/core')['useEventListener']>
    readonly useEventSource: UnwrapRef<typeof import('@vueuse/core')['useEventSource']>
    readonly useEyeDropper: UnwrapRef<typeof import('@vueuse/core')['useEyeDropper']>
    readonly useFavicon: UnwrapRef<typeof import('@vueuse/core')['useFavicon']>
    readonly useFaviconPwaIcon: UnwrapRef<typeof import('../../node_modules/@vite-pwa/nuxt/dist/runtime/composables/index')['useFaviconPwaIcon']>
    readonly useFetch: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/fetch')['useFetch']>
    readonly useFileDialog: UnwrapRef<typeof import('@vueuse/core')['useFileDialog']>
    readonly useFileSystemAccess: UnwrapRef<typeof import('@vueuse/core')['useFileSystemAccess']>
    readonly useFirebase: UnwrapRef<typeof import('../../composables/useFirebase')['useFirebase']>
    readonly useFocus: UnwrapRef<typeof import('@vueuse/core')['useFocus']>
    readonly useFocusWithin: UnwrapRef<typeof import('@vueuse/core')['useFocusWithin']>
    readonly useFormValidation: UnwrapRef<typeof import('../../composables/useFormValidation')['useFormValidation']>
    readonly useFps: UnwrapRef<typeof import('@vueuse/core')['useFps']>
    readonly useFullscreen: UnwrapRef<typeof import('@vueuse/core')['useFullscreen']>
    readonly useGamepad: UnwrapRef<typeof import('@vueuse/core')['useGamepad']>
    readonly useGeographicAnalytics: UnwrapRef<typeof import('../../composables/useGeographicAnalytics')['useGeographicAnalytics']>
    readonly useGeolocation: UnwrapRef<typeof import('@vueuse/core')['useGeolocation']>
    readonly useGeolocationWithFallback: UnwrapRef<typeof import('../../composables/geolocation')['useGeolocationWithFallback']>
    readonly useHaptics: UnwrapRef<typeof import('../../composables/useHaptics')['useHaptics']>
    readonly useHead: UnwrapRef<typeof import('../../node_modules/@unhead/vue')['useHead']>
    readonly useHeadSafe: UnwrapRef<typeof import('../../node_modules/@unhead/vue')['useHeadSafe']>
    readonly useHydration: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/hydrate')['useHydration']>
    readonly useId: UnwrapRef<typeof import('../../node_modules/vue')['useId']>
    readonly useIdle: UnwrapRef<typeof import('@vueuse/core')['useIdle']>
    readonly useInfiniteScroll: UnwrapRef<typeof import('@vueuse/core')['useInfiniteScroll']>
    readonly useIntersectionObserver: UnwrapRef<typeof import('@vueuse/core')['useIntersectionObserver']>
    readonly useInterval: UnwrapRef<typeof import('@vueuse/core')['useInterval']>
    readonly useIntervalFn: UnwrapRef<typeof import('@vueuse/core')['useIntervalFn']>
    readonly useInvoiceReminders: UnwrapRef<typeof import('../../composables/useInvoiceReminders')['useInvoiceReminders']>
    readonly useInvoiceWorkflows: UnwrapRef<typeof import('../../composables/useInvoiceWorkflows')['useInvoiceWorkflows']>
    readonly useKeyModifier: UnwrapRef<typeof import('@vueuse/core')['useKeyModifier']>
    readonly useLastChanged: UnwrapRef<typeof import('@vueuse/core')['useLastChanged']>
    readonly useLazyAsyncData: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/asyncData')['useLazyAsyncData']>
    readonly useLazyFetch: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/fetch')['useLazyFetch']>
    readonly useLink: UnwrapRef<typeof import('../../node_modules/vue-router')['useLink']>
    readonly useLoadingIndicator: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/loading-indicator')['useLoadingIndicator']>
    readonly useLocalStorage: UnwrapRef<typeof import('@vueuse/core')['useLocalStorage']>
    readonly useMLPipeline: UnwrapRef<typeof import('../../composables/useMLPipeline')['useMLPipeline']>
    readonly useMagicKeys: UnwrapRef<typeof import('@vueuse/core')['useMagicKeys']>
    readonly useManualRefHistory: UnwrapRef<typeof import('@vueuse/core')['useManualRefHistory']>
    readonly useMaskablePwaIcon: UnwrapRef<typeof import('../../node_modules/@vite-pwa/nuxt/dist/runtime/composables/index')['useMaskablePwaIcon']>
    readonly useMe: UnwrapRef<typeof import('../../composables/me')['useMe']>
    readonly useMediaControls: UnwrapRef<typeof import('@vueuse/core')['useMediaControls']>
    readonly useMediaQuery: UnwrapRef<typeof import('@vueuse/core')['useMediaQuery']>
    readonly useMemoize: UnwrapRef<typeof import('@vueuse/core')['useMemoize']>
    readonly useMemory: UnwrapRef<typeof import('@vueuse/core')['useMemory']>
    readonly useMobileNavigation: UnwrapRef<typeof import('../../composables/useMobileNavigation')['useMobileNavigation']>
    readonly useModel: UnwrapRef<typeof import('../../node_modules/vue')['useModel']>
    readonly useMounted: UnwrapRef<typeof import('@vueuse/core')['useMounted']>
    readonly useMouse: UnwrapRef<typeof import('@vueuse/core')['useMouse']>
    readonly useMouseInElement: UnwrapRef<typeof import('@vueuse/core')['useMouseInElement']>
    readonly useMousePressed: UnwrapRef<typeof import('@vueuse/core')['useMousePressed']>
    readonly useMultiLanguageEmail: UnwrapRef<typeof import('../../composables/useMultiLanguageEmail')['useMultiLanguageEmail']>
    readonly useMutationObserver: UnwrapRef<typeof import('@vueuse/core')['useMutationObserver']>
    readonly useNavigatorLanguage: UnwrapRef<typeof import('@vueuse/core')['useNavigatorLanguage']>
    readonly useNetwork: UnwrapRef<typeof import('@vueuse/core')['useNetwork']>
    readonly useNotificationPreferences: UnwrapRef<typeof import('../../composables/useNotificationPreferences')['useNotificationPreferences']>
    readonly useNotificationService: UnwrapRef<typeof import('../../composables/useNotificationService')['useNotificationService']>
    readonly useNotifications: UnwrapRef<typeof import('../../composables/notifications')['useNotifications']>
    readonly useNow: UnwrapRef<typeof import('@vueuse/core')['useNow']>
    readonly useNuxtApp: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/nuxt')['useNuxtApp']>
    readonly useNuxtData: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/asyncData')['useNuxtData']>
    readonly useNuxtDevTools: UnwrapRef<typeof import('../../node_modules/@nuxt/devtools/dist/runtime/use-nuxt-devtools')['useNuxtDevTools']>
    readonly useObjectUrl: UnwrapRef<typeof import('@vueuse/core')['useObjectUrl']>
    readonly useOfflineStorage: UnwrapRef<typeof import('../../composables/useOfflineStorage')['useOfflineStorage']>
    readonly useOffsetPagination: UnwrapRef<typeof import('@vueuse/core')['useOffsetPagination']>
    readonly useOnline: UnwrapRef<typeof import('@vueuse/core')['useOnline']>
    readonly usePDFGeneration: UnwrapRef<typeof import('../../composables/usePDFGeneration')['usePDFGeneration']>
    readonly usePWA: UnwrapRef<typeof import('../../node_modules/@vite-pwa/nuxt/dist/runtime/composables/index')['usePWA']>
    readonly usePWAInstall: UnwrapRef<typeof import('../../composables/usePWAInstall')['usePWAInstall']>
    readonly usePageLeave: UnwrapRef<typeof import('@vueuse/core')['usePageLeave']>
    readonly useParallax: UnwrapRef<typeof import('@vueuse/core')['useParallax']>
    readonly useParentElement: UnwrapRef<typeof import('@vueuse/core')['useParentElement']>
    readonly usePaymentProcessing: UnwrapRef<typeof import('../../composables/usePaymentProcessing')['usePaymentProcessing']>
    readonly usePaymentReconciliation: UnwrapRef<typeof import('../../composables/usePaymentReconciliation')['usePaymentReconciliation']>
    readonly usePerformanceObserver: UnwrapRef<typeof import('@vueuse/core')['usePerformanceObserver']>
    readonly usePermission: UnwrapRef<typeof import('@vueuse/core')['usePermission']>
    readonly usePermissions: UnwrapRef<typeof import('../../composables/permissions')['usePermissions']>
    readonly usePointer: UnwrapRef<typeof import('@vueuse/core')['usePointer']>
    readonly usePointerLock: UnwrapRef<typeof import('@vueuse/core')['usePointerLock']>
    readonly usePointerSwipe: UnwrapRef<typeof import('@vueuse/core')['usePointerSwipe']>
    readonly usePredictiveAnalytics: UnwrapRef<typeof import('../../composables/usePredictiveAnalytics')['usePredictiveAnalytics']>
    readonly usePreferredColorScheme: UnwrapRef<typeof import('@vueuse/core')['usePreferredColorScheme']>
    readonly usePreferredContrast: UnwrapRef<typeof import('@vueuse/core')['usePreferredContrast']>
    readonly usePreferredDark: UnwrapRef<typeof import('@vueuse/core')['usePreferredDark']>
    readonly usePreferredLanguages: UnwrapRef<typeof import('@vueuse/core')['usePreferredLanguages']>
    readonly usePreferredReducedMotion: UnwrapRef<typeof import('@vueuse/core')['usePreferredReducedMotion']>
    readonly usePreviewMode: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/preview')['usePreviewMode']>
    readonly usePrevious: UnwrapRef<typeof import('@vueuse/core')['usePrevious']>
    readonly useRafFn: UnwrapRef<typeof import('@vueuse/core')['useRafFn']>
    readonly useRefHistory: UnwrapRef<typeof import('@vueuse/core')['useRefHistory']>
    readonly useRequestEvent: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/ssr')['useRequestEvent']>
    readonly useRequestFetch: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/ssr')['useRequestFetch']>
    readonly useRequestHeader: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/ssr')['useRequestHeader']>
    readonly useRequestHeaders: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/ssr')['useRequestHeaders']>
    readonly useRequestURL: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/url')['useRequestURL']>
    readonly useResizeObserver: UnwrapRef<typeof import('@vueuse/core')['useResizeObserver']>
    readonly useResponseHeader: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/ssr')['useResponseHeader']>
    readonly useRoute: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/router')['useRoute']>
    readonly useRouteAnnouncer: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/route-announcer')['useRouteAnnouncer']>
    readonly useRouter: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/router')['useRouter']>
    readonly useRuntimeConfig: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/nuxt')['useRuntimeConfig']>
    readonly useRuntimeHook: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/runtime-hook')['useRuntimeHook']>
    readonly useScreenOrientation: UnwrapRef<typeof import('@vueuse/core')['useScreenOrientation']>
    readonly useScreenSafeArea: UnwrapRef<typeof import('@vueuse/core')['useScreenSafeArea']>
    readonly useScript: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScript']>
    readonly useScriptClarity: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptClarity']>
    readonly useScriptCloudflareWebAnalytics: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptCloudflareWebAnalytics']>
    readonly useScriptCrisp: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptCrisp']>
    readonly useScriptEventPage: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptEventPage']>
    readonly useScriptFathomAnalytics: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptFathomAnalytics']>
    readonly useScriptGoogleAdsense: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptGoogleAdsense']>
    readonly useScriptGoogleAnalytics: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptGoogleAnalytics']>
    readonly useScriptGoogleMaps: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptGoogleMaps']>
    readonly useScriptGoogleTagManager: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptGoogleTagManager']>
    readonly useScriptHotjar: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptHotjar']>
    readonly useScriptIntercom: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptIntercom']>
    readonly useScriptLemonSqueezy: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptLemonSqueezy']>
    readonly useScriptMatomoAnalytics: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptMatomoAnalytics']>
    readonly useScriptMetaPixel: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptMetaPixel']>
    readonly useScriptNpm: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptNpm']>
    readonly useScriptPlausibleAnalytics: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptPlausibleAnalytics']>
    readonly useScriptSegment: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptSegment']>
    readonly useScriptStripe: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptStripe']>
    readonly useScriptTag: UnwrapRef<typeof import('@vueuse/core')['useScriptTag']>
    readonly useScriptTriggerConsent: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptTriggerConsent']>
    readonly useScriptTriggerElement: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptTriggerElement']>
    readonly useScriptVimeoPlayer: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptVimeoPlayer']>
    readonly useScriptXPixel: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptXPixel']>
    readonly useScriptYouTubePlayer: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptYouTubePlayer']>
    readonly useScroll: UnwrapRef<typeof import('@vueuse/core')['useScroll']>
    readonly useScrollLock: UnwrapRef<typeof import('@vueuse/core')['useScrollLock']>
    readonly useSeoMeta: UnwrapRef<typeof import('../../node_modules/@unhead/vue')['useSeoMeta']>
    readonly useServerHead: UnwrapRef<typeof import('../../node_modules/@unhead/vue')['useServerHead']>
    readonly useServerHeadSafe: UnwrapRef<typeof import('../../node_modules/@unhead/vue')['useServerHeadSafe']>
    readonly useServerSeoMeta: UnwrapRef<typeof import('../../node_modules/@unhead/vue')['useServerSeoMeta']>
    readonly useSessionStorage: UnwrapRef<typeof import('@vueuse/core')['useSessionStorage']>
    readonly useShadowRoot: UnwrapRef<typeof import('../../node_modules/vue')['useShadowRoot']>
    readonly useShare: UnwrapRef<typeof import('@vueuse/core')['useShare']>
    readonly useSlots: UnwrapRef<typeof import('../../node_modules/vue')['useSlots']>
    readonly useSorted: UnwrapRef<typeof import('@vueuse/core')['useSorted']>
    readonly useSpeechRecognition: UnwrapRef<typeof import('@vueuse/core')['useSpeechRecognition']>
    readonly useSpeechSynthesis: UnwrapRef<typeof import('@vueuse/core')['useSpeechSynthesis']>
    readonly useState: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/state')['useState']>
    readonly useStepper: UnwrapRef<typeof import('@vueuse/core')['useStepper']>
    readonly useStorageAsync: UnwrapRef<typeof import('@vueuse/core')['useStorageAsync']>
    readonly useStyleTag: UnwrapRef<typeof import('@vueuse/core')['useStyleTag']>
    readonly useSubscriptionBilling: UnwrapRef<typeof import('../../composables/useSubscriptionBilling')['useSubscriptionBilling']>
    readonly useSupported: UnwrapRef<typeof import('@vueuse/core')['useSupported']>
    readonly useSwipe: UnwrapRef<typeof import('@vueuse/core')['useSwipe']>
    readonly useSyncQueue: UnwrapRef<typeof import('../../composables/useSyncQueue')['useSyncQueue']>
    readonly useTaxReporting: UnwrapRef<typeof import('../../composables/useTaxReporting')['useTaxReporting']>
    readonly useTemplateRef: UnwrapRef<typeof import('../../node_modules/vue')['useTemplateRef']>
    readonly useTemplateRefsList: UnwrapRef<typeof import('@vueuse/core')['useTemplateRefsList']>
    readonly useTextDirection: UnwrapRef<typeof import('@vueuse/core')['useTextDirection']>
    readonly useTextSelection: UnwrapRef<typeof import('@vueuse/core')['useTextSelection']>
    readonly useTextareaAutosize: UnwrapRef<typeof import('@vueuse/core')['useTextareaAutosize']>
    readonly useThrottle: UnwrapRef<typeof import('@vueuse/core')['useThrottle']>
    readonly useThrottleFn: UnwrapRef<typeof import('@vueuse/core')['useThrottleFn']>
    readonly useThrottledRefHistory: UnwrapRef<typeof import('@vueuse/core')['useThrottledRefHistory']>
    readonly useTimeAgo: UnwrapRef<typeof import('@vueuse/core')['useTimeAgo']>
    readonly useTimeout: UnwrapRef<typeof import('@vueuse/core')['useTimeout']>
    readonly useTimeoutFn: UnwrapRef<typeof import('@vueuse/core')['useTimeoutFn']>
    readonly useTimeoutPoll: UnwrapRef<typeof import('@vueuse/core')['useTimeoutPoll']>
    readonly useTimestamp: UnwrapRef<typeof import('@vueuse/core')['useTimestamp']>
    readonly useToNumber: UnwrapRef<typeof import('@vueuse/core')['useToNumber']>
    readonly useToString: UnwrapRef<typeof import('@vueuse/core')['useToString']>
    readonly useToggle: UnwrapRef<typeof import('@vueuse/core')['useToggle']>
    readonly useTouchGestures: UnwrapRef<typeof import('../../composables/useTouchGestures')['useTouchGestures']>
    readonly useTransition: UnwrapRef<typeof import('@vueuse/core')['useTransition']>
    readonly useTransitionState: UnwrapRef<typeof import('../../node_modules/vue')['useTransitionState']>
    readonly useTransparentPwaIcon: UnwrapRef<typeof import('../../node_modules/@vite-pwa/nuxt/dist/runtime/composables/index')['useTransparentPwaIcon']>
    readonly useUploads: UnwrapRef<typeof import('../../composables/uploads')['useUploads']>
    readonly useUrlSearchParams: UnwrapRef<typeof import('@vueuse/core')['useUrlSearchParams']>
    readonly useUserManagement: UnwrapRef<typeof import('../../composables/user-management')['useUserManagement']>
    readonly useUserMedia: UnwrapRef<typeof import('@vueuse/core')['useUserMedia']>
    readonly useUserProfile: UnwrapRef<typeof import('../../composables/useUserProfile')['useUserProfile']>
    readonly useVModel: UnwrapRef<typeof import('@vueuse/core')['useVModel']>
    readonly useVModels: UnwrapRef<typeof import('@vueuse/core')['useVModels']>
    readonly useVibrate: UnwrapRef<typeof import('@vueuse/core')['useVibrate']>
    readonly useVirtualList: UnwrapRef<typeof import('@vueuse/core')['useVirtualList']>
    readonly useVisualization: UnwrapRef<typeof import('../../composables/useVisualization')['useVisualization']>
    readonly useWakeLock: UnwrapRef<typeof import('@vueuse/core')['useWakeLock']>
    readonly useWebNotification: UnwrapRef<typeof import('@vueuse/core')['useWebNotification']>
    readonly useWebSocket: UnwrapRef<typeof import('@vueuse/core')['useWebSocket']>
    readonly useWebWorker: UnwrapRef<typeof import('@vueuse/core')['useWebWorker']>
    readonly useWebWorkerFn: UnwrapRef<typeof import('@vueuse/core')['useWebWorkerFn']>
    readonly useWindowFocus: UnwrapRef<typeof import('@vueuse/core')['useWindowFocus']>
    readonly useWindowScroll: UnwrapRef<typeof import('@vueuse/core')['useWindowScroll']>
    readonly useWindowSize: UnwrapRef<typeof import('@vueuse/core')['useWindowSize']>
    readonly useWorkflowTemplates: UnwrapRef<typeof import('../../composables/useWorkflowTemplates')['useWorkflowTemplates']>
    readonly user: UnwrapRef<typeof import('../../composables/user')['user']>
    readonly validateDayOfWeek: UnwrapRef<typeof import('../../composables/useNotificationPreferences')['validateDayOfWeek']>
    readonly validateNotificationFrequency: UnwrapRef<typeof import('../../composables/useNotificationPreferences')['validateNotificationFrequency']>
    readonly validateNotificationPreferences: UnwrapRef<typeof import('../../composables/useNotificationPreferences')['validateNotificationPreferences']>
    readonly validateTimeOfDay: UnwrapRef<typeof import('../../composables/useNotificationPreferences')['validateTimeOfDay']>
    readonly watch: UnwrapRef<typeof import('../../node_modules/vue')['watch']>
    readonly watchArray: UnwrapRef<typeof import('@vueuse/core')['watchArray']>
    readonly watchAtMost: UnwrapRef<typeof import('@vueuse/core')['watchAtMost']>
    readonly watchDebounced: UnwrapRef<typeof import('@vueuse/core')['watchDebounced']>
    readonly watchDeep: UnwrapRef<typeof import('@vueuse/core')['watchDeep']>
    readonly watchEffect: UnwrapRef<typeof import('../../node_modules/vue')['watchEffect']>
    readonly watchIgnorable: UnwrapRef<typeof import('@vueuse/core')['watchIgnorable']>
    readonly watchImmediate: UnwrapRef<typeof import('@vueuse/core')['watchImmediate']>
    readonly watchOnce: UnwrapRef<typeof import('@vueuse/core')['watchOnce']>
    readonly watchPausable: UnwrapRef<typeof import('@vueuse/core')['watchPausable']>
    readonly watchPostEffect: UnwrapRef<typeof import('../../node_modules/vue')['watchPostEffect']>
    readonly watchSyncEffect: UnwrapRef<typeof import('../../node_modules/vue')['watchSyncEffect']>
    readonly watchThrottled: UnwrapRef<typeof import('@vueuse/core')['watchThrottled']>
    readonly watchTriggerable: UnwrapRef<typeof import('@vueuse/core')['watchTriggerable']>
    readonly watchWithFilter: UnwrapRef<typeof import('@vueuse/core')['watchWithFilter']>
    readonly whenever: UnwrapRef<typeof import('@vueuse/core')['whenever']>
    readonly withCtx: UnwrapRef<typeof import('../../node_modules/vue')['withCtx']>
    readonly withDirectives: UnwrapRef<typeof import('../../node_modules/vue')['withDirectives']>
    readonly withKeys: UnwrapRef<typeof import('../../node_modules/vue')['withKeys']>
    readonly withMemo: UnwrapRef<typeof import('../../node_modules/vue')['withMemo']>
    readonly withModifiers: UnwrapRef<typeof import('../../node_modules/vue')['withModifiers']>
    readonly withScopeId: UnwrapRef<typeof import('../../node_modules/vue')['withScopeId']>
  }
}