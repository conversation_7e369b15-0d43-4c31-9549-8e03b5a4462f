<template>
  <div class="min-h-screen font-sans relative overflow-hidden">
    <!-- Dark gradient background -->
    <div class="absolute inset-0 bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900"></div>
    
    <!-- Decorative elements -->
    <div class="absolute top-20 right-20 w-96 h-96 bg-gradient-to-br from-blue-400/20 to-purple-400/20 rounded-full blur-3xl"></div>
    <div class="absolute bottom-40 left-20 w-80 h-80 bg-gradient-to-br from-purple-400/20 to-blue-400/20 rounded-full blur-3xl"></div>
    <div class="absolute top-1/2 right-1/3 w-64 h-64 bg-gradient-to-br from-red-400/15 to-pink-400/15 rounded-full blur-2xl"></div>

    <role-guard role="Admin" :show-access-denied="true" redirect-to="/c/dashboard">
      <div class="relative z-10 px-6 py-8 space-y-8">
        <div class="max-w-screen-xl mx-auto">
          <NuxtLink to="/c/admin/users" class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-blue-600/20 to-blue-700/20 hover:from-blue-600/30 hover:to-blue-700/30 text-blue-400 hover:text-blue-300 font-medium rounded-lg transition-all duration-200 transform hover:scale-[1.02] focus:outline-none focus:ring-2 focus:ring-blue-400 focus:ring-opacity-50 border border-blue-400/20 backdrop-blur-sm">
            <Icon name="mdi:arrow-left" class="mr-2 h-4 w-4" />
            Back to User Management
          </NuxtLink>
        </div>

        <!-- Loading state -->
        <div v-if="loading" class="max-w-screen-xl mx-auto">
          <div class="bg-gradient-to-br from-gray-900/70 via-gray-800/70 to-gray-900/70 backdrop-blur-sm rounded-2xl p-12 border border-white/10 text-center">
            <div class="flex flex-col items-center justify-center space-y-4">
              <div class="animate-spin rounded-full h-12 w-12 border-4 border-white/20 border-t-blue-400"></div>
              <p class="text-lg font-medium text-white">Loading user profile...</p>
            </div>
          </div>
        </div>

        <!-- Error state -->
        <div v-else-if="error" class="max-w-screen-xl mx-auto">
          <div class="bg-gradient-to-br from-red-900/70 via-red-800/70 to-red-900/70 backdrop-blur-sm rounded-2xl p-6 border border-red-400/20">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <Icon name="mdi:alert-circle" class="h-6 w-6 text-red-400" aria-hidden="true" />
              </div>
              <div class="ml-3">
                <p class="text-sm text-red-300">{{ error }}</p>
              </div>
            </div>
          </div>
        </div>

        <!-- User Profile Content -->
        <div v-else class="max-w-screen-xl mx-auto space-y-8">
          <!-- User Header -->
          <div class="bg-gradient-to-br from-gray-900/70 via-gray-800/70 to-gray-900/70 backdrop-blur-sm rounded-2xl border border-white/10 shadow-2xl overflow-hidden">
            <div class="p-8 flex flex-col md:flex-row md:items-center md:justify-between gap-6">
              <div class="flex items-center space-x-6">
                <div class="flex-shrink-0">
                  <div class="h-24 w-24 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center text-3xl text-white overflow-hidden shadow-lg">
                    <img v-if="user.avatar && user.avatar.src" :src="user.avatar.src" alt="User avatar" class="h-full w-full object-cover" />
                    <span v-else class="font-semibold">{{ user.first_name ? user.first_name.charAt(0) : 'U' }}</span>
                  </div>
                </div>
                <div>
                  <h1 class="text-3xl font-bold text-white mb-2">
                    {{ user.first_name }} {{ user.last_name }}
                    <span v-if="user.roles && user.roles.includes('Admin')"
                          class="ml-3 inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-500/20 text-blue-300 border border-blue-400/20">
                      <Icon name="mdi:shield-account" class="mr-1 h-4 w-4" />
                      Admin
                    </span>
                    <span v-if="!user.active"
                          class="ml-3 inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-red-500/20 text-red-300 border border-red-400/20">
                      <Icon name="mdi:account-off" class="mr-1 h-4 w-4" />
                      Inactive
                    </span>
                  </h1>
                  <p class="text-lg font-medium text-gray-300 flex items-center">
                    <Icon name="mdi:email" class="mr-2 h-5 w-5 text-gray-400" />
                    {{ user.email }}
                    <span v-if="user.email_verified" class="ml-2 text-green-400" title="Email Verified">
                      <Icon name="mdi:check-circle" class="h-5 w-5 inline" />
                    </span>
                  </p>
                  <p v-if="user.created_at || user.created_date || user.createdAt" class="mt-2 text-sm text-gray-400 flex items-center">
                    <Icon name="mdi:calendar-plus" class="mr-2 h-4 w-4" />
                    Member since {{ formatDate(user.created_at || user.created_date || user.createdAt) }}
                  </p>
                </div>
              </div>

              <div class="flex flex-wrap gap-3">
                <button
                  @click="toggleStatus"
                  :class="[
                    'px-6 py-3 rounded-lg text-sm font-medium focus:outline-none focus:ring-2 focus:ring-offset-2 transition-all duration-200 border backdrop-blur-sm',
                    user.active
                      ? 'border-red-400/20 text-red-300 bg-red-500/20 hover:bg-red-500/30 focus:ring-red-400'
                      : 'border-green-400/20 text-green-300 bg-green-500/20 hover:bg-green-500/30 focus:ring-green-400'
                  ]"
                >
                  <Icon :name="user.active ? 'mdi:account-off' : 'mdi:account-check'" class="mr-2 h-4 w-4" />
                  {{ user.active ? 'Deactivate User' : 'Activate User' }}
                </button>

                <button
                  @click="activeTab = 'communication'"
                  class="px-6 py-3 border border-white/20 bg-blue-600/20 hover:bg-blue-600/30 rounded-lg text-sm font-medium text-blue-300 hover:text-blue-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-400 transition-all duration-200 backdrop-blur-sm"
                >
                  <Icon name="mdi:message" class="h-4 w-4 inline mr-2" />
                  Send Message
                </button>
              </div>
            </div>
          </div>

          <!-- Tabs -->
          <div class="bg-gradient-to-br from-gray-900/70 via-gray-800/70 to-gray-900/70 backdrop-blur-sm rounded-2xl border border-white/10 overflow-hidden">
            <div class="border-b border-white/10">
              <nav class="flex space-x-8 px-6" aria-label="Tabs">
                <button
                  v-for="tab in tabs"
                  :key="tab.name"
                  @click="currentTab = tab.id"
                  :class="[
                    currentTab === tab.id
                      ? 'border-blue-400 text-blue-400 bg-blue-500/10'
                      : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300',
                    'whitespace-nowrap py-4 px-4 border-b-2 font-medium text-sm transition-all duration-200 rounded-t-lg'
                  ]"
                >
                  <Icon :name="tab.icon" class="h-5 w-5 inline mr-2" />
                  {{ tab.name }}
                </button>
              </nav>
            </div>

            <!-- Tab Content -->
            <div class="p-6">
              <div v-if="currentTab === 'profile'" class="space-y-6">
                <!-- User Profile Information -->
                <div class="bg-gradient-to-br from-gray-800/50 via-gray-700/50 to-gray-800/50 backdrop-blur-sm rounded-xl border border-white/10 overflow-hidden">
                  <div class="px-6 py-5 flex justify-between items-center border-b border-white/10">
                    <div>
                      <h3 class="text-lg leading-6 font-medium text-white">User Information</h3>
                      <p class="mt-1 max-w-2xl text-sm text-gray-300">Personal details and contact information.</p>
                    </div>
                    <button
                      @click="editMode = !editMode"
                      class="inline-flex items-center px-4 py-2 border border-white/20 shadow-sm text-sm leading-4 font-medium rounded-lg text-gray-300 bg-gray-700/50 hover:bg-gray-600/50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-400 transition-all duration-200 backdrop-blur-sm"
                    >
                      <Icon :name="editMode ? 'mdi:close' : 'mdi:pencil'" class="h-4 w-4 mr-2" />
                      {{ editMode ? 'Cancel' : 'Edit' }}
                    </button>
                  </div>

                  <div class="p-6">
                    <user-profile
                      :userId="userId"
                      :edit-mode="editMode"
                      @save="saveUserProfile"
                      @cancel="editMode = false"
                    />
                  </div>
                </div>

                <!-- User Role Management -->
                <div class="bg-gradient-to-br from-gray-800/50 via-gray-700/50 to-gray-800/50 backdrop-blur-sm rounded-xl border border-white/10 overflow-hidden">
                  <div class="px-6 py-5 border-b border-white/10">
                    <h3 class="text-lg leading-6 font-medium text-white">Role Management</h3>
                    <p class="mt-1 max-w-2xl text-sm text-gray-300">Manage user roles and permissions.</p>
                  </div>
                  <div class="p-6">
                    <role-management :user-id="userId" :multiple-users="false" :show-save-button="true" />
                  </div>
                </div>

                <!-- User Statistics and Activity Trends -->
                <div class="bg-gradient-to-br from-gray-800/50 via-gray-700/50 to-gray-800/50 backdrop-blur-sm rounded-xl border border-white/10 overflow-hidden">
                  <div class="px-6 py-5 border-b border-white/10">
                    <h3 class="text-lg leading-6 font-medium text-white">User Statistics</h3>
                    <p class="mt-1 max-w-2xl text-sm text-gray-300">Activity statistics and usage trends.</p>
                  </div>
                  <div class="p-6">
                    <user-statistics :user-id="userId" />
                  </div>
                </div>
              </div>

              <div v-else-if="currentTab === 'activity'" class="bg-gradient-to-br from-gray-800/50 via-gray-700/50 to-gray-800/50 backdrop-blur-sm rounded-xl border border-white/10 overflow-hidden">
                <div class="px-6 py-5 border-b border-white/10">
                  <h3 class="text-lg leading-6 font-medium text-white">User Activity</h3>
                  <p class="mt-1 max-w-2xl text-sm text-gray-300">Logins, actions, and activity history.</p>
                </div>
                <div class="p-6">
                  <user-activity-log :user-id="userId" />
                </div>
              </div>

              <div v-else-if="currentTab === 'content'" class="bg-gradient-to-br from-gray-800/50 via-gray-700/50 to-gray-800/50 backdrop-blur-sm rounded-xl border border-white/10 overflow-hidden">
                <div class="px-6 py-5 border-b border-white/10">
                  <h3 class="text-lg leading-6 font-medium text-white">User Content</h3>
                  <p class="mt-1 max-w-2xl text-sm text-gray-300">Business cards, flyers, and other uploaded content.</p>
                </div>
                <div class="p-6">
                  <user-content-list :user-id="userId" />
                </div>
              </div>

              <div v-else-if="currentTab === 'communication'" class="bg-gradient-to-br from-gray-800/50 via-gray-700/50 to-gray-800/50 backdrop-blur-sm rounded-xl border border-white/10 overflow-hidden">
                <div class="px-6 py-5 border-b border-white/10">
                  <h3 class="text-lg leading-6 font-medium text-white">User Communication</h3>
                  <p class="mt-1 max-w-2xl text-sm text-gray-300">Send messages and view communication history.</p>
                </div>
                <div class="p-6">
                  <user-communication
                    :user-email="user.email"
                    :user-id="userId"
                    :show-templates="true"
                    @message-sent="handleMessageSent"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Send Message Modal -->
        <teleport to="body">
          <transition name="modal">
            <div v-if="showMessageModal" class="fixed z-10 inset-0 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
              <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
                <div class="fixed inset-0 bg-gray-900/75 backdrop-blur-sm transition-opacity" aria-hidden="true" @click="showMessageModal = false"></div>

                <div class="inline-block align-bottom bg-gradient-to-br from-gray-900/95 via-gray-800/95 to-gray-900/95 backdrop-blur-sm rounded-2xl px-6 pt-6 pb-6 text-left overflow-hidden shadow-2xl border border-white/10 transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
                  <div class="sm:flex sm:items-start">
                    <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-xl bg-blue-500/20 border border-blue-400/20 sm:mx-0 sm:h-10 sm:w-10">
                      <Icon name="mdi:message" class="h-6 w-6 text-blue-400" aria-hidden="true" />
                    </div>
                    <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                      <h3 class="text-lg leading-6 font-medium text-white" id="modal-title">
                        Send Message to {{ user.first_name }} {{ user.last_name }}
                      </h3>
                      <div class="mt-2">
                        <p class="text-sm text-gray-300">
                          This message will be sent to the user's email address.
                        </p>
                        <div class="mt-4 space-y-4">
                          <div>
                            <label for="subject" class="block text-sm font-medium text-gray-300">Subject</label>
                            <input
                              type="text"
                              id="subject"
                              v-model="messageData.subject"
                              class="mt-1 block w-full border border-white/20 rounded-lg shadow-sm py-2 px-3 focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-blue-400 sm:text-sm bg-gray-800/50 text-white placeholder-gray-400 backdrop-blur-sm"
                            />
                          </div>
                          <div>
                            <label for="message" class="block text-sm font-medium text-gray-300">Message</label>
                            <textarea
                              id="message"
                              v-model="messageData.message"
                              rows="5"
                              class="mt-1 block w-full border border-white/20 rounded-lg shadow-sm py-2 px-3 focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-blue-400 sm:text-sm bg-gray-800/50 text-white placeholder-gray-400 backdrop-blur-sm"
                            ></textarea>
                          </div>
                          <div class="flex items-start">
                            <div class="flex items-center h-5">
                              <input
                                id="send-copy"
                                v-model="messageData.sendCopy"
                                type="checkbox"
                                class="focus:ring-blue-400 h-4 w-4 text-blue-500 border-white/20 bg-gray-800/50 rounded"
                              />
                            </div>
                            <div class="ml-3 text-sm">
                              <label for="send-copy" class="font-medium text-gray-300">Send me a copy</label>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="mt-5 sm:mt-4 sm:flex sm:flex-row-reverse">
                    <button
                      type="button"
                      @click="sendMessage"
                      :disabled="sending"
                      class="w-full inline-flex justify-center rounded-lg border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-400 sm:ml-3 sm:w-auto sm:text-sm transition-all duration-200 disabled:opacity-50"
                    >
                      <Icon name="mdi:loading" class="h-4 w-4 mr-2 animate-spin" v-if="sending" />
                      Send Message
                    </button>
                    <button
                      type="button"
                      @click="showMessageModal = false"
                      class="mt-3 w-full inline-flex justify-center rounded-lg border border-white/20 shadow-sm px-4 py-2 bg-gray-700/50 text-base font-medium text-gray-300 hover:bg-gray-600/50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-400 sm:mt-0 sm:w-auto sm:text-sm transition-all duration-200 backdrop-blur-sm"
                    >
                      Cancel
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </transition>
        </teleport>
      </div>
    </role-guard>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRoute } from 'vue-router'
import moment from 'moment'
import { useUserManagement } from '~/composables/user-management'
import { useEmail } from '~/composables/useEmail'

definePageMeta({
  layout: 'dashboard',
})

const route = useRoute()
const userId = computed(() => route.query.id as string)

const { getUserById, updateUserProfile } = useUserManagement()

// State
const user = ref<any>({})
const loading = ref(true)
const error = ref('')
const editMode = ref(false)
const currentTab = ref('profile')
const showMessageModal = ref(false)
const sending = ref(false)
const activeTab = ref('profile')
const messageData = ref({
  subject: '',
  message: '',
  sendCopy: false
})

// Tabs definition
const tabs = [
  { id: 'profile', name: 'Profile', icon: 'mdi:account' },
  { id: 'activity', name: 'Activity', icon: 'mdi:history' },
  { id: 'content', name: 'Content', icon: 'mdi:file-document' },
  { id: 'communication', name: 'Communication', icon: 'mdi:message' }
]

// Fetch user data
const fetchUser = async () => {
  loading.value = true
  error.value = ''
  console.log('route', route.query)
  console.log('Fetching user:', userId.value)

  try {
    const userData = await getUserById(userId.value)

    if (userData) {
      user.value = userData
    } else {
      error.value = 'User not found'
    }
  } catch (err: any) {
    error.value = err.message || 'Error loading user data'
  } finally {
    loading.value = false
  }
}

// Format date helper
const formatDate = (date: any) => {
  if (!date) return ''

  try {
    return moment(date).format('MMMM D, YYYY')
  } catch (err) {
    return ''
  }
}

// Toggle user active status
const toggleStatus = async () => {
  try {
    const { toggleUserStatus } = useUserManagement()
    const result = await toggleUserStatus(userId.value, !user.value.active)

    if (result.success) {
      user.value.active = !user.value.active
    } else {
      throw new Error(result.error || 'Failed to update user status')
    }
  } catch (err: any) {
    error.value = err.message || 'Error updating user status'
  }
}

// Save user profile changes
const saveUserProfile = async (profileData: any) => {
  try {
    const result = await updateUserProfile(userId.value, profileData)

    if (result.success) {
      // Update local user object with new data
      user.value = { ...user.value, ...profileData }
      editMode.value = false
    } else {
      throw new Error(result.error || 'Failed to update user profile')
    }
  } catch (err: any) {
    error.value = err.message || 'Error updating user profile'
  }
}

// Handle message sent event
const handleMessageSent = () => {
  // Show success message
  alert('Message sent successfully!')
}

// Send message to user
const sendMessage = async () => {
  if (!messageData.value.subject || !messageData.value.message) {
    error.value = 'Please provide both subject and message'
    return
  }

  sending.value = true

  try {
    // This will need to be implemented in the future, for now let's simulate it
    await new Promise(resolve => setTimeout(resolve, 1000))

    // We'll need to integrate with the email system later
    // For now just close the modal
    showMessageModal.value = false

    // Display success message
    alert('Message sent successfully!')
  } catch (err: any) {
    error.value = err.message || 'Error sending message'
  } finally {
    sending.value = false
  }
}

// Load user data on component mount
onMounted(() => {
  fetchUser()
})
</script>
