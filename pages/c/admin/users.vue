<template>
  <div class="min-h-screen font-sans relative overflow-hidden">
    <!-- Dark gradient background -->
    <div class="absolute inset-0 bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900"></div>
    
    <!-- Decorative elements -->
    <div class="absolute top-20 right-20 w-96 h-96 bg-gradient-to-br from-blue-400/20 to-purple-400/20 rounded-full blur-3xl"></div>
    <div class="absolute bottom-40 left-20 w-80 h-80 bg-gradient-to-br from-purple-400/20 to-blue-400/20 rounded-full blur-3xl"></div>
    <div class="absolute top-1/2 right-1/3 w-64 h-64 bg-gradient-to-br from-red-400/15 to-pink-400/15 rounded-full blur-2xl"></div>

    <role-guard role="Admin" :show-access-denied="true" redirect-to="/c/dashboard">
      <div class="relative z-10 px-6 py-8 space-y-8">
        <!-- Header Section -->
        <div class="max-w-screen-xl mx-auto">
          <div class="bg-gradient-to-br from-gray-900/90 via-gray-800/90 to-gray-900/90 backdrop-blur-sm rounded-2xl p-8 border border-white/10 shadow-2xl">
            <div class="flex flex-col md:flex-row justify-between items-start md:items-center">
              <div>
                <div class="flex items-center mb-4">
                  <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center mr-4 shadow-lg">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-6 w-6 text-white">
                      <path stroke-linecap="round" stroke-linejoin="round" d="M15 19.128a9.38 9.38 0 002.625.372 9.337 9.337 0 004.121-.952 4.125 4.125 0 00-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 018.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0111.964-3.07M12 6.375a3.375 3.375 0 11-6.75 0 3.375 3.375 0 016.75 0zm8.25 2.25a2.625 2.625 0 11-5.25 0 2.625 2.625 0 015.25 0z" />
                    </svg>
                  </div>
                  <div>
                    <h1 class="text-3xl font-bold text-white">User Management</h1>
                    <p class="text-blue-400 font-medium">Manage Users, Roles & Permissions</p>
                  </div>
                </div>
                <p class="text-gray-300 max-w-3xl leading-relaxed">
                  Comprehensive user management dashboard to control access, roles, and monitor user activity across your platform.
                </p>
              </div>
              <div class="flex items-center space-x-3 mt-6 md:mt-0">
                <NuxtLink to="/c/admin" class="px-6 py-3 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-medium rounded-lg transition-all duration-200 transform hover:scale-[1.02] focus:outline-none focus:ring-2 focus:ring-blue-400 focus:ring-opacity-50 shadow-lg hover:shadow-xl flex items-center">
                  <Icon name="mdi:arrow-left" class="h-5 w-5 mr-2" />
                  Back to Admin Dashboard
                </NuxtLink>
              </div>
            </div>
          </div>
        </div>        <!-- User Stats Cards -->
        <div class="max-w-screen-xl mx-auto">
          <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="bg-gradient-to-br from-gray-900/70 via-gray-800/70 to-gray-900/70 backdrop-blur-sm rounded-2xl p-6 border border-white/10 hover:border-white/20 transition-all duration-300 hover:shadow-2xl transform hover:scale-[1.02]">
              <div class="flex items-center justify-between">
                <div>
                  <p class="text-sm font-medium text-blue-400 uppercase tracking-wide">Total Users</p>
                  <p class="text-3xl font-bold text-white mt-2">{{ userStats.total }}</p>
                </div>
                <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center shadow-lg">
                  <Icon name="mdi:account-multiple" class="text-white text-xl" />
                </div>
              </div>
            </div>

            <div class="bg-gradient-to-br from-gray-900/70 via-gray-800/70 to-gray-900/70 backdrop-blur-sm rounded-2xl p-6 border border-white/10 hover:border-white/20 transition-all duration-300 hover:shadow-2xl transform hover:scale-[1.02]">
              <div class="flex items-center justify-between">
                <div>
                  <p class="text-sm font-medium text-purple-400 uppercase tracking-wide">Admin Users</p>
                  <p class="text-3xl font-bold text-white mt-2">{{ userStats.admins }}</p>
                </div>
                <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg">
                  <Icon name="mdi:shield-account" class="text-white text-xl" />
                </div>
              </div>
            </div>

            <div class="bg-gradient-to-br from-gray-900/70 via-gray-800/70 to-gray-900/70 backdrop-blur-sm rounded-2xl p-6 border border-white/10 hover:border-white/20 transition-all duration-300 hover:shadow-2xl transform hover:scale-[1.02]">
              <div class="flex items-center justify-between">
                <div>
                  <p class="text-sm font-medium text-green-400 uppercase tracking-wide">Active Users</p>
                  <p class="text-3xl font-bold text-white mt-2">{{ userStats.active }}</p>
                </div>
                <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center shadow-lg">
                  <Icon name="mdi:account-check" class="text-white text-xl" />
                </div>
              </div>
            </div>

            <div class="bg-gradient-to-br from-gray-900/70 via-gray-800/70 to-gray-900/70 backdrop-blur-sm rounded-2xl p-6 border border-white/10 hover:border-white/20 transition-all duration-300 hover:shadow-2xl transform hover:scale-[1.02]">
              <div class="flex items-center justify-between">
                <div>
                  <p class="text-sm font-medium text-amber-400 uppercase tracking-wide">New Users</p>
                  <p class="text-sm text-gray-300 font-medium">Last 30 Days</p>
                  <p class="text-3xl font-bold text-white mt-2">{{ userStats.newUsers }}</p>
                </div>
                <div class="w-12 h-12 bg-gradient-to-br from-amber-500 to-amber-600 rounded-xl flex items-center justify-center shadow-lg">
                  <Icon name="mdi:account-plus" class="text-white text-xl" />
                </div>
              </div>
            </div>
          </div>
        </div>
        </div>

        <!-- User Filter -->
        <div class="max-w-screen-xl mx-auto mb-8">
          <div class="bg-gradient-to-br from-gray-900/70 via-gray-800/70 to-gray-900/70 backdrop-blur-sm rounded-2xl p-6 border border-white/10">
            <user-filter
              :roles="availableRoles"
              :initial-filters="filters"
              @filter-change="handleFilterChange"
            />
          </div>
        </div>

        <!-- Bulk Actions -->
        <div class="max-w-screen-xl mx-auto mb-8">
          <div class="bg-gradient-to-br from-gray-900/70 via-gray-800/70 to-gray-900/70 backdrop-blur-sm rounded-2xl p-6 border border-white/10">
            <user-bulk-actions
              :selected-count="selectedUsers.length"
              :roles="selectedUserRoles"
              :statuses="selectedUserStatuses"
              @action="handleBulkAction"
              @clear-selection="clearSelection"
            />
          </div>
        </div>

        <!-- User Listing -->
        <div class="max-w-screen-xl mx-auto">
          <div class="bg-gradient-to-br from-gray-900/70 via-gray-800/70 to-gray-900/70 backdrop-blur-sm rounded-2xl border border-white/10 shadow-2xl overflow-hidden">
            <div class="px-6 py-5 border-b border-white/10">
              <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                  <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center shadow-lg">
                    <Icon name="mdi:account-group" class="h-5 w-5 text-white" />
                  </div>
                <div>
                  <h3 class="text-lg font-semibold text-white">
                    Users Directory
                  </h3>
                  <p class="text-sm text-gray-300">
                    Manage user accounts and permissions
                  </p>
                </div>
              </div>
              <div class="flex items-center space-x-3">
                <!-- Sort options -->
                <div class="flex items-center space-x-2">
                  <label for="sort-by" class="text-sm font-medium text-gray-300">Sort:</label>
                  <select
                    id="sort-by"
                    v-model="filters.sortBy"
                    @change="applyFilters"
                    class="block px-3 py-2 text-sm border border-white/20 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-400 focus:border-blue-400 bg-gray-800/50 text-gray-200 backdrop-blur-sm transition-all duration-200"
                  >
                    <option value="created_at">Created Date</option>
                    <option value="last_login">Last Login</option>
                    <option value="email">Email</option>
                    <option value="first_name">First Name</option>
                  </select>
                </div>

                <select
                  id="sort-direction"
                  v-model="filters.sortDirection"
                  @change="applyFilters"
                  class="block px-3 py-2 text-sm border border-white/20 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-400 focus:border-blue-400 bg-gray-800/50 text-gray-200 backdrop-blur-sm transition-all duration-200"
                >
                  <option value="asc">↑ Ascending</option>
                  <option value="desc">↓ Descending</option>
                </select>
              </div>
            </div>
          </div>

          <!-- Loading state -->
          <div v-if="isLoading" class="px-6 py-16 text-center">
            <div class="flex flex-col items-center justify-center space-y-4">
              <div class="inline-block animate-spin rounded-full h-12 w-12 border-4 border-white/20 border-t-blue-400"></div>
              <p class="text-lg font-medium text-white">Loading users...</p>
              <p class="text-sm text-gray-300">Please wait while we fetch the latest data</p>
            </div>
          </div>

          <!-- Empty state -->
          <div v-else-if="filteredUsers.length === 0" class="px-6 py-16 text-center">
            <div class="flex flex-col items-center justify-center space-y-4">
              <div class="bg-gray-800/50 backdrop-blur-sm rounded-full p-6 border border-white/10">
                <Icon name="mdi:account-off" class="h-16 w-16 text-gray-400" />
              </div>
              <div class="space-y-2">
                <h3 class="text-xl font-semibold text-white">No users found</h3>
                <p class="text-base text-gray-300 max-w-md">
                  We couldn't find any users matching your current search criteria. Try adjusting your filters or search terms.
                </p>
              </div>
              <button
                @click="resetFilters"
                class="inline-flex items-center px-6 py-3 border border-white/20 text-sm font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-400 transition-all duration-200 backdrop-blur-sm"
              >
                <Icon name="mdi:filter-remove" class="mr-2 h-5 w-5" />
                Clear All Filters
              </button>
            </div>
          </div>

          <!-- User list -->
          <div v-else>
            <ul class="divide-y divide-white/10">              <li v-for="user in filteredUsers" :key="user.id" class="px-6 py-5 hover:bg-white/5 transition-all duration-200 group border-b border-white/5 last:border-b-0">
                <div class="flex items-center justify-between">
                  <div class="flex items-center flex-1 min-w-0">
                    <!-- Selection checkbox -->
                    <div class="mr-4 flex-shrink-0">
                      <input
                        type="checkbox"
                        :id="`user-${user.id}`"
                        :checked="isUserSelected(user)"
                        @change="toggleUserSelection(user)"
                        class="h-4 w-4 text-blue-400 focus:ring-2 focus:ring-blue-400 border-white/20 bg-gray-800/50 rounded transition-colors duration-200"
                      />
                    </div>

                    <!-- User avatar and info -->
                    <div class="flex-shrink-0 h-12 w-12 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center overflow-hidden shadow-lg">
                      <img 
                        v-if="user.avatar && user.avatar.src" 
                        :src="user.avatar.src" 
                        :alt="`${user.first_name} ${user.last_name}`" 
                        class="h-full w-full object-cover" 
                      />
                      <span v-else class="text-white font-semibold text-lg">
                        {{ user.first_name ? user.first_name.charAt(0).toUpperCase() : 'U' }}
                      </span>
                    </div>
                    
                    <div class="ml-4 flex-1 min-w-0">
                      <div class="flex items-center flex-wrap gap-2 mb-1">
                        <NuxtLink 
                          :to="`/c/admin/users/${user.id}`" 
                          class="text-base font-semibold text-white hover:text-blue-400 transition-colors duration-200 truncate"
                        >
                          {{ user.first_name }} {{ user.last_name }}
                        </NuxtLink>
                        <div class="flex items-center gap-2">
                          <span
                            v-if="user.roles && user.roles.includes('Admin')"
                            class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-500/20 text-blue-300 border border-blue-400/20"
                          >
                            <Icon name="mdi:shield-account" class="mr-1 h-3 w-3" />
                            Admin
                          </span>
                          <span
                            class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                            :class="user.active !== false 
                              ? 'bg-green-500/20 text-green-300 border border-green-400/20' 
                              : 'bg-red-500/20 text-red-300 border border-red-400/20'"
                          >
                            <div
                              class="mr-1 h-2 w-2 rounded-full"
                              :class="user.active !== false ? 'bg-green-400' : 'bg-red-400'"
                            ></div>
                            {{ user.active !== false ? 'Active' : 'Inactive' }}
                          </span>
                        </div>
                      </div>
                      
                      <div class="text-sm text-gray-300 mb-1 truncate">
                        <Icon name="mdi:email" class="inline h-4 w-4 mr-1 text-gray-400" />
                        {{ user.email }}
                      </div>
                      
                      <div class="text-xs text-gray-400 flex items-center gap-4">
                        <span class="flex items-center">
                          <Icon name="mdi:calendar-plus" class="mr-1 h-3 w-3" />
                          Created: {{ formatDate(user.created_at || user.created_date) }}
                        </span>
                        <span v-if="user.last_login" class="flex items-center">
                          <Icon name="mdi:login" class="mr-1 h-3 w-3" />
                          Last login: {{ formatDate(user.last_login) }}
                        </span>
                      </div>
                    </div>
                  </div>

                  <!-- Action buttons -->
                  <div class="flex items-center space-x-2 ml-4 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                    <button
                      @click="toggleUserRole(user)"
                      :class="[
                        'inline-flex items-center px-3 py-1.5 text-xs font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 border backdrop-blur-sm',
                        isUserAdmin(user) 
                          ? 'bg-amber-500/20 text-amber-300 hover:bg-amber-500/30 border-amber-400/20 focus:ring-amber-400' 
                          : 'bg-blue-500/20 text-blue-300 hover:bg-blue-500/30 border-blue-400/20 focus:ring-blue-400'
                      ]"
                      :title="isUserAdmin(user) ? 'Remove admin privileges' : 'Grant admin privileges'"
                    >
                      <Icon :name="isUserAdmin(user) ? 'mdi:shield-remove' : 'mdi:shield-plus'" class="mr-1 h-3 w-3" />
                      {{ isUserAdmin(user) ? 'Remove Admin' : 'Make Admin' }}
                    </button>
                    
                    <button
                      @click="handleToggleUserStatus(user)"
                      :class="[
                        'inline-flex items-center px-3 py-1.5 text-xs font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 border backdrop-blur-sm',
                        user.active !== false 
                          ? 'bg-red-500/20 text-red-300 hover:bg-red-500/30 border-red-400/20 focus:ring-red-400' 
                          : 'bg-green-500/20 text-green-300 hover:bg-green-500/30 border-green-400/20 focus:ring-green-400'
                      ]"
                      :title="user.active !== false ? 'Deactivate user account' : 'Activate user account'"
                    >
                      <Icon :name="user.active !== false ? 'mdi:account-off' : 'mdi:account-check'" class="mr-1 h-3 w-3" />
                      {{ user.active !== false ? 'Deactivate' : 'Activate' }}
                    </button>
                    
                    <button
                      @click="navigateTo(`/c/admin/user-single?id=${user.id}`)"
                      class="inline-flex items-center px-3 py-1.5 text-xs font-medium rounded-lg bg-gray-700/50 text-gray-300 hover:bg-gray-600/50 border border-white/10 hover:border-white/20 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 backdrop-blur-sm"
                      title="View user profile"
                    >
                      <Icon name="mdi:account-details" class="mr-1 h-3 w-3" />
                      View Profile
                  </button>
                  </div>
                </div>
              </li>
            </ul>

            <!-- Pagination -->
            <div class="px-6 py-4 flex items-center justify-between border-t border-white/10 bg-gradient-to-r from-gray-900/50 via-gray-800/50 to-gray-900/50 backdrop-blur-sm">
              <div class="flex items-center space-x-4">
                <p class="text-sm font-medium text-gray-300">
                  Showing <span class="font-semibold text-white">{{ filteredUsers.length }}</span> 
                  user{{ filteredUsers.length !== 1 ? 's' : '' }}
                </p>
                <div v-if="selectedUsers.length > 0" class="flex items-center space-x-2">
                  <div class="h-4 w-px bg-white/20"></div>
                  <p class="text-sm text-blue-400 font-medium">
                    {{ selectedUsers.length }} selected
                  </p>
                </div>
              </div>
              
              <button
                v-if="hasMoreUsers"
                @click="loadMoreUsers"
                :disabled="isLoading"
                class="inline-flex items-center px-4 py-2 border border-white/20 text-sm font-medium rounded-lg text-gray-300 bg-gray-800/50 hover:bg-gray-700/50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed shadow-sm backdrop-blur-sm"
              >
                <Icon v-if="isLoading" name="mdi:loading" class="mr-2 h-4 w-4 animate-spin text-blue-400" />
                <Icon v-else name="mdi:chevron-down" class="mr-2 h-4 w-4 text-gray-400" />
                {{ isLoading ? 'Loading...' : 'Load More Users' }}
              </button>
            </div>
          </div>
        </div>
      </div>
    </role-guard>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import moment from 'moment'
import { useUserManagement } from '~/composables/user-management'

definePageMeta({
  layout: 'dashboard',
})

// Initialize user management composable
const {
  filteredUsers,
  selectedUsers,
  isLoading,
  userStats,
  hasMoreUsers,
  filters,
  initUserManagement,
  fetchUserStats,
  fetchUsers,
  loadMoreUsers,
  applyFilters,
  resetFilters,
  toggleUserSelection,
  isUserSelected,
  updateUserRole,
  toggleUserStatus,
  performBulkAction
} = useUserManagement()

// Available roles for filtering
const availableRoles = ref(['Admin', 'User', 'Editor', 'Moderator'])

// Computed properties for bulk actions
const selectedUserRoles = computed(() => {
  const hasAdmin = selectedUsers.value.some(user => user.roles && user.roles.includes('Admin'))
  const hasNonAdmin = selectedUsers.value.some(user => !user.roles || !user.roles.includes('Admin'))
  return { admin: hasAdmin, other: hasNonAdmin }
})

const selectedUserStatuses = computed(() => {
  const hasActive = selectedUsers.value.some(user => user.active !== false)
  const hasInactive = selectedUsers.value.some(user => user.active === false)
  return { active: hasActive, inactive: hasInactive }
})

// Helper functions
const formatDate = (date: any) => {
  if (!date) return 'N/A'
  return moment(date).format('MMM D, YYYY')
}

const isUserAdmin = (user: any) => {
  return user.roles && user.roles.includes('Admin')
}

// Event handlers
const handleFilterChange = (newFilters: any) => {
  // Map user-filter component filters to user-management composable filters
  filters.value = {
    ...filters.value,
    searchTerm: newFilters.search || '',
    role: newFilters.role || 'all',
    status: newFilters.status || 'all',
    dateRange: {
      start: newFilters.dateFrom ? new Date(newFilters.dateFrom) : null,
      end: newFilters.dateTo ? new Date(newFilters.dateTo) : null
    },
    sortBy: newFilters.sortBy || 'created_at',
    sortDirection: newFilters.sortDirection || 'desc'
  }

  applyFilters()
}

const handleBulkAction = async (action: string) => {
  await performBulkAction(action)
  // Refresh data after bulk action
  await fetchUsers(true)
  await fetchUserStats()
}

const clearSelection = () => {
  selectedUsers.value = []
}

const toggleUserRole = async (user: any) => {
  const isAdmin = user.roles && user.roles.includes('Admin')
  await updateUserRole(user.id, !isAdmin)
  // Refresh data after role change
  await fetchUsers(false)
  await fetchUserStats()
}

// Function to handle toggling user status
const handleToggleUserStatus = async (user: any) => {
  const isActive = user.active !== false
  await toggleUserStatus(user.id, !isActive)
  // Refresh data after status change
  await fetchUsers(false)
  await fetchUserStats()
}

// Initialize on component mount
onMounted(async () => {
  initUserManagement()
  await fetchUsers()
})
</script>
