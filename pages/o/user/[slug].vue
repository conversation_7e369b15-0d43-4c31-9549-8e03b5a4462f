<script setup lang="ts">
import { useAnalyticsTracking } from '~/composables/analytics-tracking';

definePageMeta({
  layout: 'clean',
})

const route = useRoute()
const slug = route.params.slug as string

// State
const currentClient = ref<any>({})
const currentSpace = ref<any>({})
const isLoading = ref(true)
const hasError = ref(false)
const errorMessage = ref('')

// Initialize analytics tracking
const { trackCardView, trackContactAction } = useAnalyticsTracking()

// Fetch business card by slug
const fetchBusinessCard = async () => {
  try {
    isLoading.value = true
    hasError.value = false
    
    console.log('Fetching business card for slug:', slug)
    
    // Query business card by slug
    const cards = await queryByWhere2('businesscards', 'slug', slug, '==')
    
    if (cards && cards.result && cards.result.length > 0) {
      currentClient.value = cards.result[0]
      console.log('Found business card:', currentClient.value)
      
      // Fetch the associated space
      if (currentClient.value.space_own) {
        const space = await queryById('spaces', currentClient.value.space_own)
        if (space) {
          currentSpace.value = space
        }
      }
      
      // Track the business card view
      try {
        await trackCardView(currentClient.value.id, 'qr_scan')
        console.log('QR scan tracked for business card:', currentClient.value.id)
      } catch (error) {
        console.error('Error tracking QR scan:', error)
      }
    } else {
      console.log('No business card found for slug:', slug)
      hasError.value = true
      errorMessage.value = 'Business card not found'
    }
  } catch (error) {
    console.error('Error fetching business card:', error)
    hasError.value = true
    errorMessage.value = 'Failed to load business card'
  } finally {
    isLoading.value = false
  }
}

// Contact action handler
const handleContactAction = (type: string) => {
  try {
    trackContactAction(currentClient.value.id, type)
    console.log(`Contact action tracked: ${type}`)
  } catch (error) {
    console.error('Error tracking contact action:', error)
  }
}

// Phone number formatter
const formatPhoneNumber = (phone: string) => {
  if (!phone) return ''
  // Basic phone number formatting
  return phone.replace(/(\d{3})(\d{3})(\d{4})/, '($1) $2-$3')
}

// Load business card on mount
onMounted(() => {
  fetchBusinessCard()
})

// SEO meta tags
useHead({
  title: computed(() => {
    if (currentClient.value.name) {
      return `${currentClient.value.name} - Business Card`
    }
    if (currentClient.value.first_name || currentClient.value.last_name) {
      return `${currentClient.value.first_name} ${currentClient.value.last_name} - Business Card`
    }
    return 'Business Card'
  }),
  meta: [
    {
      name: 'description',
      content: computed(() => {
        const name = currentClient.value.name || `${currentClient.value.first_name || ''} ${currentClient.value.last_name || ''}`.trim()
        const company = currentClient.value.company || currentSpace.value.name
        return `Contact information for ${name}${company ? ` at ${company}` : ''}`
      })
    },
    {
      property: 'og:title',
      content: computed(() => {
        const name = currentClient.value.name || `${currentClient.value.first_name || ''} ${currentClient.value.last_name || ''}`.trim()
        return `${name} - Business Card`
      })
    },
    {
      property: 'og:description',
      content: computed(() => {
        const name = currentClient.value.name || `${currentClient.value.first_name || ''} ${currentClient.value.last_name || ''}`.trim()
        const company = currentClient.value.company || currentSpace.value.name
        return `Contact information for ${name}${company ? ` at ${company}` : ''}`
      })
    },
    {
      property: 'og:type',
      content: 'profile'
    }
  ]
})
</script>

<template>
  <div class="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
    <!-- Loading State -->
    <div v-if="isLoading" class="flex items-center justify-center min-h-screen">
      <div class="text-center">
        <div class="w-16 h-16 border-4 border-blue-200 border-t-blue-600 rounded-full animate-spin mx-auto mb-4"></div>
        <p class="text-gray-600">Loading business card...</p>
      </div>
    </div>

    <!-- Error State -->
    <div v-else-if="hasError" class="flex items-center justify-center min-h-screen">
      <div class="text-center max-w-md mx-auto px-6">
        <div class="w-24 h-24 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        </div>
        <h2 class="text-2xl font-bold text-gray-900 mb-4">{{ errorMessage }}</h2>
        <p class="text-gray-600 mb-6">The business card you're looking for could not be found or may have been removed.</p>
        <button
          @click="$router.push('/')"
          class="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200"
        >
          Go Home
        </button>
      </div>
    </div>

    <!-- Business Card Display -->
    <div v-else class="max-w-2xl mx-auto px-4 py-8">
      <!-- Header -->
      <div class="text-center mb-8">
        <div class="w-20 h-20 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
          </svg>
        </div>
        <h1 class="text-3xl font-bold text-gray-900 mb-2">
          {{ currentClient.name || `${currentClient.first_name || ''} ${currentClient.last_name || ''}`.trim() || 'Business Card' }}
        </h1>
        <p v-if="currentClient.company || currentSpace.name" class="text-xl text-gray-600">
          {{ currentClient.company || currentSpace.name }}
        </p>
        <p v-if="currentClient.position || currentClient.title" class="text-lg text-gray-500 mt-1">
          {{ currentClient.position || currentClient.title }}
        </p>
      </div>

      <!-- Business Card Image -->
      <div v-if="currentClient.image && currentClient.image.src" class="mb-8">
        <div class="bg-white rounded-xl shadow-lg overflow-hidden">
          <img
            :src="currentClient.image.src"
            :alt="`Business card for ${currentClient.name || currentClient.first_name + ' ' + currentClient.last_name}`"
            class="w-full h-auto object-cover"
          />
        </div>
      </div>

      <!-- About Section -->
      <div v-if="currentClient.about" class="bg-white rounded-xl shadow-lg p-6 mb-6">
        <h2 class="text-xl font-semibold text-gray-900 mb-3">About</h2>
        <div class="text-gray-700 prose prose-blue max-w-none" v-html="currentClient.about"></div>
      </div>

      <!-- Contact Information -->
      <div class="bg-white rounded-xl shadow-lg p-6 mb-6">
        <h2 class="text-xl font-semibold text-gray-900 mb-4">Contact Information</h2>
        
        <div class="space-y-4">
          <!-- Full Name -->
          <div v-if="currentClient.first_name || currentClient.last_name" class="flex items-center">
            <div class="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center mr-4 flex-shrink-0">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
              </svg>
            </div>
            <div>
              <p class="text-sm font-medium text-gray-500">Full Name</p>
              <p class="text-gray-900">{{ currentClient.first_name }} {{ currentClient.last_name }}</p>
            </div>
          </div>

          <!-- Mobile -->
          <div v-if="currentClient.cell" class="flex items-center">
            <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center mr-4 flex-shrink-0">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z" />
              </svg>
            </div>
            <div class="flex-1">
              <p class="text-sm font-medium text-gray-500">Mobile</p>
              <a
                :href="'tel:' + currentClient.cell"
                @click="handleContactAction('mobile')"
                class="text-blue-600 hover:text-blue-800 hover:underline transition-colors duration-200 font-medium"
              >
                {{ formatPhoneNumber(currentClient.cell) }}
              </a>
            </div>
          </div>

          <!-- Phone -->
          <div v-if="currentClient.phone" class="flex items-center">
            <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-4 flex-shrink-0">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
              </svg>
            </div>
            <div class="flex-1">
              <p class="text-sm font-medium text-gray-500">Phone</p>
              <a
                :href="'tel:' + currentClient.phone"
                @click="handleContactAction('phone')"
                class="text-blue-600 hover:text-blue-800 hover:underline transition-colors duration-200 font-medium"
              >
                {{ formatPhoneNumber(currentClient.phone) }}
              </a>
            </div>
          </div>

          <!-- Fax -->
          <div v-if="currentClient.fax" class="flex items-center">
            <div class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center mr-4 flex-shrink-0">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m0 0V3a1 1 0 011 1v7.5M7 4V3a1 1 0 00-1 1v7.5m0 0a3 3 0 103 3v-3m-3 0a3 3 0 00-3 3v-3" />
              </svg>
            </div>
            <div class="flex-1">
              <p class="text-sm font-medium text-gray-500">Fax</p>
              <a
                :href="'fax:' + currentClient.fax"
                @click="handleContactAction('fax')"
                class="text-blue-600 hover:text-blue-800 hover:underline transition-colors duration-200 font-medium"
              >
                {{ formatPhoneNumber(currentClient.fax) }}
              </a>
            </div>
          </div>

          <!-- Email -->
          <div v-if="currentClient.email" class="flex items-center">
            <div class="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center mr-4 flex-shrink-0">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
              </svg>
            </div>
            <div class="flex-1">
              <p class="text-sm font-medium text-gray-500">Email</p>
              <a
                :href="'mailto:' + currentClient.email"
                @click="handleContactAction('email')"
                class="text-blue-600 hover:text-blue-800 hover:underline transition-colors duration-200 font-medium break-all"
              >
                {{ currentClient.email }}
              </a>
            </div>
          </div>

          <!-- Website -->
          <div v-if="currentClient.website" class="flex items-center">
            <div class="w-10 h-10 bg-indigo-100 rounded-lg flex items-center justify-center mr-4 flex-shrink-0">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9m0 9c-5 0-9-4-9-9s4-9 9-9" />
              </svg>
            </div>
            <div class="flex-1">
              <p class="text-sm font-medium text-gray-500">Website</p>
              <a
                :href="currentClient.website.startsWith('http') ? currentClient.website : 'https://' + currentClient.website"
                @click="handleContactAction('website')"
                target="_blank"
                rel="noopener noreferrer"
                class="text-blue-600 hover:text-blue-800 hover:underline transition-colors duration-200 font-medium break-all"
              >
                {{ currentClient.website }}
              </a>
            </div>
          </div>

          <!-- Location -->
          <div v-if="currentClient.formatted_address" class="flex items-center">
            <div class="w-10 h-10 bg-yellow-100 rounded-lg flex items-center justify-center mr-4 flex-shrink-0">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-yellow-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
              </svg>
            </div>
            <div class="flex-1">
              <p class="text-sm font-medium text-gray-500">Location</p>
              <p class="text-gray-900">{{ currentClient.formatted_address }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Note/Description -->
      <div v-if="currentClient.note" class="bg-white rounded-xl shadow-lg p-6 mb-6">
        <h2 class="text-xl font-semibold text-gray-900 mb-3">Note</h2>
        <p class="text-gray-700">{{ currentClient.note }}</p>
      </div>

      <!-- Powered by Covalonic -->
      <div class="text-center py-6">
        <p class="text-sm text-gray-500">
          Powered by <a href="https://covalonic.com" target="_blank" class="text-blue-600 hover:text-blue-800 font-medium">Covalonic</a>
        </p>
      </div>
    </div>
  </div>
</template>
