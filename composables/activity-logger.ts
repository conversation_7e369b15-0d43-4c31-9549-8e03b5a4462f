import { database } from './database'

export const useActivityLogger = () => {
  /**
   * Log user activity
   * @param userId - The user ID
   * @param action - The action performed
   * @param details - Additional details about the action
   * @param metadata - Optional metadata
   */
  const logActivity = async (
    userId: string,
    action: string,
    details: any = {},
    metadata: any = {}
  ) => {
    try {
      const { addToCollection } = database()
      
      const activityLog = {
        user_id: userId,
        action,
        type: 'activity',
        details,
        metadata,
        timestamp: new Date(),
        ip_address: await getClientIP(),
        user_agent: navigator.userAgent,
        session_id: getSessionId()
      }
      
      await addToCollection('activity', activityLog)
    } catch (error) {
      console.error('Error logging activity:', error)
    }
  }

  /**
   * Log login activity
   * @param userId - The user ID
   * @param status - Login status (success/failed)
   * @param details - Additional details
   */
  const logLogin = async (
    userId: string,
    status: 'success' | 'failed',
    details: any = {}
  ) => {
    try {
      const { addToCollection } = database()
      
      const loginLog = {
        uid: userId,
        user_id: userId,
        status,
        type: 'login',
        details,
        timestamp: new Date(),
        date: new Date(),
        ip_address: await getClientIP(),
        device: getDeviceInfo(),
        location: await getLocation(),
        user_agent: navigator.userAgent,
        session_id: getSessionId()
      }
      
      await addToCollection('login_history', loginLog)
    } catch (error) {
      console.error('Error logging login:', error)
    }
  }

  /**
   * Log content creation activity
   * @param userId - The user ID
   * @param contentType - Type of content (businesscard, flyer, etc.)
   * @param contentId - ID of the created content
   * @param title - Title of the content
   */
  const logContentCreation = async (
    userId: string,
    contentType: string,
    contentId: string,
    title: string
  ) => {
    await logActivity(userId, 'create_content', {
      content_type: contentType,
      content_id: contentId,
      title,
      action_type: 'create'
    })
  }

  /**
   * Log content update activity
   * @param userId - The user ID
   * @param contentType - Type of content
   * @param contentId - ID of the updated content
   * @param changes - What was changed
   */
  const logContentUpdate = async (
    userId: string,
    contentType: string,
    contentId: string,
    changes: string[]
  ) => {
    await logActivity(userId, 'update_content', {
      content_type: contentType,
      content_id: contentId,
      changes,
      action_type: 'update'
    })
  }

  /**
   * Log content deletion activity
   * @param userId - The user ID
   * @param contentType - Type of content
   * @param contentId - ID of the deleted content
   */
  const logContentDeletion = async (
    userId: string,
    contentType: string,
    contentId: string
  ) => {
    await logActivity(userId, 'delete_content', {
      content_type: contentType,
      content_id: contentId,
      action_type: 'delete'
    })
  }

  /**
   * Get client IP address
   */
  const getClientIP = async () => {
    try {
      // In a real app, you'd get this from the server or a service
      // For now, return a placeholder
      return 'unknown'
    } catch {
      return 'unknown'
    }
  }

  /**
   * Get device information
   */
  const getDeviceInfo = () => {
    const userAgent = navigator.userAgent
    
    if (/Mobile|Android|iPhone|iPad/.test(userAgent)) {
      return 'mobile'
    } else if (/Tablet/.test(userAgent)) {
      return 'tablet'
    } else {
      return 'desktop'
    }
  }

  /**
   * Get location information (simplified)
   */
  const getLocation = async () => {
    try {
      // In a real app, you'd use geolocation or IP-based location
      // For now, return a placeholder
      return 'unknown'
    } catch {
      return 'unknown'
    }
  }

  /**
   * Get or create session ID
   */
  const getSessionId = () => {
    let sessionId = sessionStorage.getItem('session_id')
    if (!sessionId) {
      sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      sessionStorage.setItem('session_id', sessionId)
    }
    return sessionId
  }

  /**
   * Create sample activity logs for testing
   * @param userId - The user ID to create logs for
   */
  const createSampleLogs = async (userId: string) => {
    const now = new Date()
    const logs = [
      // Login activities
      {
        uid: userId,
        user_id: userId,
        type: 'login',
        status: 'success',
        timestamp: new Date(now.getTime() - 1000 * 60 * 30), // 30 minutes ago
        date: new Date(now.getTime() - 1000 * 60 * 30),
        ip_address: '*************',
        device: 'desktop',
        location: 'Cape Town, South Africa',
        user_agent: navigator.userAgent,
        session_id: getSessionId(),
        details: {}
      },
      {
        uid: userId,
        user_id: userId,
        type: 'login',
        status: 'success',
        timestamp: new Date(now.getTime() - 1000 * 60 * 60 * 2), // 2 hours ago
        date: new Date(now.getTime() - 1000 * 60 * 60 * 2),
        ip_address: '*************',
        device: 'mobile',
        location: 'Cape Town, South Africa',
        user_agent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X)',
        session_id: 'session_' + (Date.now() - 1000 * 60 * 60 * 2),
        details: {}
      },
      // User activities
      {
        user_id: userId,
        type: 'activity',
        action: 'create_content',
        title: 'Created Business Card',
        description: 'User created a new business card',
        timestamp: new Date(now.getTime() - 1000 * 60 * 45), // 45 minutes ago
        ip_address: '*************',
        user_agent: navigator.userAgent,
        session_id: getSessionId(),
        details: {
          content_type: 'businesscard',
          content_id: 'card_123',
          title: 'John Doe Business Card',
          action_type: 'create'
        },
        metadata: {}
      },
      {
        user_id: userId,
        type: 'activity',
        action: 'update_content',
        title: 'Updated Profile',
        description: 'User updated their profile information',
        timestamp: new Date(now.getTime() - 1000 * 60 * 60), // 1 hour ago
        ip_address: '*************',
        user_agent: navigator.userAgent,
        session_id: getSessionId(),
        details: {
          content_type: 'profile',
          changes: ['first_name', 'phone_number'],
          action_type: 'update'
        },
        metadata: {}
      },
      {
        user_id: userId,
        type: 'activity',
        action: 'create_content',
        title: 'Created Flyer',
        description: 'User created a new promotional flyer',
        timestamp: new Date(now.getTime() - 1000 * 60 * 60 * 3), // 3 hours ago
        ip_address: '*************',
        user_agent: navigator.userAgent,
        session_id: getSessionId(),
        details: {
          content_type: 'flyer',
          content_id: 'flyer_456',
          title: 'Summer Sale Flyer',
          action_type: 'create'
        },
        metadata: {}
      }
    ]

    try {
      const { addToCollection } = database()
      
      for (const log of logs) {
        if (log.type === 'login') {
          await addToCollection('login_history', log)
        } else {
          await addToCollection('activity', log)
        }
      }
      
      console.log(`Created ${logs.length} sample activity logs for user ${userId}`)
    } catch (error) {
      console.error('Error creating sample logs:', error)
    }
  }

  return {
    logActivity,
    logLogin,
    logContentCreation,
    logContentUpdate,
    logContentDeletion,
    createSampleLogs
  }
}