<script setup lang="ts">
import UploadEnhancedFormUploader from '~/components/upload/EnhancedFormUploader.vue';
import BusinesscardsOcrProcessor from '~/components/businesscards/OcrProcessor.vue';
import UiCard from '~/components/ui/Card.vue';

defineProps({
  open: {
    type: Boolean,
    default: () => {
      return true;
    },
  },
  homepage: {
    type: Boolean,
    default: false,
  },
});

// For OCR processing
const selectedImage = ref(null);
const ocrComplete = ref(false);
const ocrData = ref(null);
const ocrStatus = ref({ status: 'idle', progress: 0 });
const ocrValidationState = ref({ valid: false, errors: {} });

// Helper function to create object URL safely
const createObjectURL = (file: any) => {
  if (file && typeof URL !== 'undefined') {
    return URL.createObjectURL(file);
  }
  return '';
};

// Handle file selection
const handleFilesSelected = (files: any) => {
  if (files && files.length > 0) {
    selectedImage.value = files[0];
    ocrComplete.value = false;
  }
};

// Handle file cropped
const handleFileCropped = (file) => {
  console.log('File cropped:', file);
  selectedImage.value = file;
  ocrComplete.value = false;

  // Add a small delay to ensure the file is properly set before OCR processing
  setTimeout(() => {
    console.log('Starting OCR after crop with file:', selectedImage.value);
    // Force OCR to start if it hasn't already
    if (selectedImage.value && ocrStatus.value.status !== 'processing') {
      ocrStatus.value = { status: 'started', progress: 0 };
    }
  }, 100);
};

// Handle OCR completion
const handleExtractionComplete = (data) => {
  ocrComplete.value = true;
  ocrData.value = data;
};

// Handle OCR status updates
const handleProcessingStatus = (status) => {
  console.log('OCR status update:', status);
  ocrStatus.value = status;

  // If there's an error, show it in the console for debugging
  if (status.status === 'error') {
    console.error('OCR processing error details:', status.error);
  }
};

// Handle OCR validation
const handleOcrValidation = (validation) => {
  ocrValidationState.value = validation;
};

const formsItem = ref({
  title: "Business Cards",
  filters: {
    index: "omni",
    collection: "businesscards",
    queryBy: "",
    route_add: "/c/businesscards-create",

    description: "Businesscards",
    areas: ["Business Card Information"],
    filterBy:
      "Touched Records, Untouched Records, Record Action, Related Records Action",
  },
  data: [
    {
      label: "id",
      val: "id",
      value: "",
      class: "",
      class_input: "o_input",
      class_label: "o_label_small",
      type: "text",
      form: false,
      table: false,
      small_table: false,
      small_form: false,
      editable: true,
      card: false,
      card_type: "",
      required: true,
      readonly: false,
      unique: false,
      hidden: false,
      disabled: false,
      area: 1,
      quick: false,
      component: "FormsInputsType",
      action: "FormsActionsType",
    },
    {
      label: "Created At",
      val: "created_at",
      value: "",
      class: "",
      class_input: "o_input",
      class_label: "o_label_small",
      type: "text",
      form: false,
      table: false,
      small_table: false,
      small_form: false,
      editable: true,
      card: false,
      card_type: "",
      required: true,
      readonly: false,
      unique: false,
      hidden: false,
      disabled: false,
      area: 1,
      quick: false,
      component: "FormsInputsType",
      action: "FormsActionsType",
    },
    {
      label: "Updated at",
      val: "updated_at",
      value: "",
      class: "",
      class_input: "o_input",
      class_label: "o_label_small",
      type: "text",
      form: false,
      table: false,
      small_table: false,
      small_form: false,
      editable: true,
      card: false,
      card_type: "",
      required: true,
      readonly: false,
      unique: false,
      hidden: false,
      disabled: false,
      area: 1,
      quick: false,
      component: "FormsInputsType",
      action: "FormsActionsType",
    },
    {
      label: "User ownership",
      val: "user_own",
      value: "",
      class: "",
      class_input: "o_input",
      class_label: "o_label_small",
      type: "text",
      form: false,
      table: false,
      small_table: false,
      small_form: false,
      editable: true,
      card: false,
      card_type: "",
      required: true,
      readonly: false,
      unique: false,
      hidden: false,
      disabled: false,
      area: 1,
      quick: false,
      component: "FormsInputsType",
      action: "FormsActionsType",
    },
    {
      label: "Users with access",
      val: "user_access",
      value: "",
      class: "",
      class_input: "o_input",
      class_label: "o_label_small",
      type: "text",
      form: false,
      table: false,
      small_table: false,
      small_form: false,
      editable: true,
      card: false,
      card_type: "",
      required: true,
      readonly: false,
      unique: false,
      hidden: false,
      disabled: false,
      area: 1,
      quick: false,
      component: "FormsInputsType",
      action: "FormsActionsType",
    },
    {
      label: "Space ownership",
      val: "space_own",
      value: "",
      class: "",
      class_input: "o_input",
      class_label: "o_label_small",
      type: "text",
      form: false,
      table: false,
      small_table: false,
      small_form: false,
      editable: true,
      card: false,
      card_type: "",
      required: true,
      readonly: false,
      unique: false,
      hidden: false,
      disabled: false,
      area: 1,
      quick: false,
      component: "FormsInputsType",
      action: "FormsActionsType",
    },
    {
      label: "Space with access",
      val: "space_access",
      value: "",
      class: "",
      class_input: "o_input",
      class_label: "o_label_small",
      type: "text",
      form: false,
      table: false,
      small_table: false,
      small_form: false,
      editable: true,
      card: false,
      card_type: "",
      required: true,
      readonly: false,
      unique: false,
      hidden: false,
      disabled: false,
      area: 1,
      quick: false,
      component: "FormsInputsType",
      action: "FormsActionsType",
    },
    {
      label: "Business Card",
      val: "image",
      value: "",
      class: "col-span-2",
      class_input: "o_input",
      class_label: "o_label_small",
      type: "text",
      form: true,
      table: true,
      small_table: false,
      small_form: false,
      editable: true,
      card: false,
      card_type: "Image",
      required: true,
      readonly: false,
      unique: false,
      hidden: false,
      disabled: false,
      area: 1,
      quick: false,
      multi: false,
      optionsUrl: "plain",
      component: "FormsInputsUpload",
      action: "forms-actions-docs",
    },
    {
      label: "Name",
      val: "name",
      value: "",
      class: "",
      class_input: "o_input",
      class_label: "o_label_small",
      type: "text",
      form: true,
      table: true,
      small_table: false,
      small_form: false,
      editable: true,
      card: false,
      card_type: "",
      required: true,
      readonly: false,
      unique: false,
      hidden: false,
      disabled: false,
      area: 1,
      quick: false,
      multi: false,
      optionsUrl: "plain",
      options: [],
      component: "FormsInputsType",
      action: "forms-actions-type",
    },
    {
      label: "Tags (comma seperated)",
      val: "tags",
      value: "",
      class: "",
      class_input: "o_input",
      class_label: "o_label_small",
      type: "text",
      form: true,
      table: true,
      small_table: false,
      small_form: false,
      editable: true,
      card: false,
      card_type: "",
      required: true,
      readonly: false,
      unique: false,
      hidden: false,
      disabled: false,
      area: 1,
      quick: false,
      multi: false,
      optionsUrl: "plain",
      options: [],
      component: "FormsInputsTags",
      action: "forms-actions-tags",
    },
    {
      label: "Description",
      val: "description",
      value: "",
      class: "col-span-2",
      class_input: "o_input",
      class_label: "o_label_small",
      type: "text",
      form: true,
      table: false,
      small_table: false,
      small_form: false,
      editable: true,
      card: false,
      card_type: "",
      required: true,
      readonly: false,
      unique: false,
      hidden: false,
      disabled: false,
      area: 1,
      quick: false,
      multi: false,
      optionsUrl: "plain",
      options: [],
      component: "FormsInputsTextarea",
      action: "forms-actions-quill",
    },

    {
      label: "Status",
      val: "status",
      value: "Pending",
      class: "",
      class_input: "o_input",
      class_label: "o_label_small",
      type: "text",
      form: false,
      table: true,
      small_table: false,
      small_form: false,
      editable: false,
      card: false,
      card_type: "Image",
      required: true,
      readonly: false,
      unique: false,
      hidden: false,
      disabled: false,
      area: 1,
      quick: false,
      multi: false,
      optionsUrl: "plain",
      component: "FormsInputsSelect",
      action: "forms-actions-select",
      options: ["Approved", "Pending", "Declined"],
    },
    {
      label: "First Name",
      val: "first_name",
      value: "",
      class: "",
      class_input: "o_input",
      class_label: "o_label_small",
      type: "text",
      form: true,
      table: true,
      small_table: false,
      small_form: false,
      editable: true,
      card: false,
      card_type: "",
      required: true,
      readonly: false,
      unique: false,
      hidden: false,
      disabled: false,
      area: 1,
      quick: false,
      multi: false,
      optionsUrl: "plain",
      options: [],
      component: "FormsInputsType",
      action: "forms-actions-type",
    },
    {
      label: "Last Name",
      val: "last_name",
      value: "",
      class: "",
      class_input: "o_input",
      class_label: "o_label_small",
      type: "text",
      form: true,
      table: true,
      small_table: false,
      small_form: false,
      editable: true,
      card: false,
      card_type: "",
      required: true,
      readonly: false,
      unique: false,
      hidden: false,
      disabled: false,
      area: 1,
      quick: false,
      multi: false,
      optionsUrl: "plain",
      options: [],
      component: "FormsInputsType",
      action: "forms-actions-type",
    },
    {
      label: "Email",
      val: "email",
      value: "",
      class: "",
      class_input: "o_input",
      class_label: "o_label_small",
      type: "text",
      form: true,
      table: true,
      small_table: false,
      small_form: false,
      editable: true,
      card: false,
      card_type: "",
      required: true,
      readonly: false,
      unique: false,
      hidden: false,
      disabled: false,
      area: 1,
      quick: false,
      multi: false,
      optionsUrl: "plain",
      options: [],
      component: "FormsInputsType",
      action: "forms-actions-type",
    },

    {
      label: "Website",
      val: "website",
      value: "",
      class: "",
      class_input: "o_input",
      class_label: "o_label_small",
      type: "text",
      form: true,
      table: true,
      small_table: false,
      small_form: false,
      editable: true,
      card: false,
      card_type: "",
      required: false,
      readonly: false,
      unique: false,
      hidden: false,
      disabled: false,
      area: 1,
      quick: false,
      multi: false,
      optionsUrl: "plain",
      options: [],
      component: "FormsInputsType",
      action: "forms-actions-type",
    },
    {
      label: "Phone",
      val: "phone",
      value: "",
      class: "",
      class_input: "o_input",
      class_label: "o_label_small",
      type: "text",
      form: true,
      table: true,
      small_table: false,
      small_form: false,
      editable: true,
      card: false,
      card_type: "",
      required: true,
      readonly: false,
      unique: false,
      hidden: false,
      disabled: false,
      area: 1,
      quick: false,
      multi: false,
      optionsUrl: "plain",
      options: [],
      component: "FormsInputsType",
      action: "forms-actions-type",
    },
  ],
});

const userSpaces = useState<any>("userSpaces", () => {
  return [];
});
const created = (data: any) => {
  userSpaces.value.push(data);
};
</script>

<template>
  <div>
    <!-- OCR Processing Section -->
    <UiCard v-if="selectedImage" title="OCR Processing" variant="bordered" elevated class="mb-6">
      <!-- Preview Image -->
      <div class="mb-4 flex justify-center">
        <img
          v-if="selectedImage"
          :src="createObjectURL(selectedImage)"
          alt="Business Card Preview"
          class="max-h-48 rounded-lg shadow-md"
        />
      </div>

      <!-- OCR Status Indicator -->
      <div class="mb-4">
        <div v-if="ocrStatus.status === 'started'" class="flex items-center justify-center p-4 bg-blue-50 dark:bg-blue-900 rounded-lg">
          <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          <span class="text-blue-700 dark:text-blue-300">Starting OCR processing...</span>
        </div>

        <div v-else-if="ocrStatus.status === 'processing'" class="flex items-center justify-center p-4 bg-blue-50 dark:bg-blue-900 rounded-lg">
          <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          <span class="text-blue-700 dark:text-blue-300">Processing OCR... {{ Math.round(ocrStatus.progress * 100) }}%</span>
        </div>

        <div v-else-if="ocrStatus.status === 'completed'" class="flex items-center justify-center p-4 bg-green-50 dark:bg-green-900 rounded-lg">
          <svg class="h-5 w-5 mr-2 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
          </svg>
          <span class="text-green-700 dark:text-green-300">OCR processing complete! Form fields have been populated.</span>
        </div>

        <div v-else-if="ocrStatus.status === 'error'" class="flex items-center justify-center p-4 bg-red-50 dark:bg-red-900 rounded-lg">
          <svg class="h-5 w-5 mr-2 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
          <span class="text-red-700 dark:text-red-300">Error during OCR processing. Please try again.</span>
        </div>
      </div>

      <!-- OCR Processor -->
      <BusinesscardsOcrProcessor
        :image="selectedImage"
        @extraction-complete="handleExtractionComplete"
        @processing-status="handleProcessingStatus"
        @validation="handleOcrValidation"
      />
    </UiCard>

    <!-- Business Card Form -->
    <UploadEnhancedFormUploader
      title="Create Business Card"
      description="Upload a business card or enter contact information"
      :formItem="formsItem"
      :homepage="homepage"
      :enableCropping="true"
      :cropAspectRatio="1.78"
      imageField="image"
      :ocrData="ocrData"
      @files-selected="handleFilesSelected"
      @file-cropped="handleFileCropped"
      @created="created"
    />
  </div>
</template>