<template>
  <div class="bg-gradient-to-br from-gray-800/50 via-gray-700/50 to-gray-800/50 backdrop-blur-sm rounded-xl border border-white/10 overflow-hidden">
    <div class="px-6 py-5 flex justify-between items-center border-b border-white/10">
      <div>
        <h3 class="text-lg leading-6 font-medium text-white">
          User Profile
        </h3>
        <p class="mt-1 max-w-2xl text-sm text-gray-300">
          {{ isEditing ? 'Edit user details' : 'Personal details and application settings' }}
        </p>
      </div>
      
      <div class="flex items-center space-x-3">
        <!-- Edit/Save buttons -->
        <button 
          v-if="!isEditing"
          @click="startEditing"
          class="inline-flex items-center px-4 py-2 border border-white/20 shadow-sm text-sm leading-4 font-medium rounded-lg text-gray-300 bg-gray-700/50 hover:bg-gray-600/50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-400 transition-all duration-200 backdrop-blur-sm"
        >
          <Icon name="mdi:pencil" class="h-4 w-4 mr-2" />
          Edit Profile
        </button>
        
        <div v-else class="flex space-x-2">
          <button 
            @click="cancelEditing"
            class="inline-flex items-center px-3 py-2 border border-white/20 shadow-sm text-sm leading-4 font-medium rounded-lg text-gray-300 bg-gray-700/50 hover:bg-gray-600/50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-400 transition-all duration-200 backdrop-blur-sm"
          >
            Cancel
          </button>
          
          <button 
            @click="saveChanges"
            :disabled="isSaving"
            class="inline-flex items-center px-3 py-2 border border-transparent shadow-sm text-sm leading-4 font-medium rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-400 transition-all duration-200"
            :class="[
              'bg-blue-600 hover:bg-blue-700',
              isSaving ? 'opacity-50 cursor-not-allowed' : ''
            ]"
          >
            <Icon name="mdi:loading" class="h-4 w-4 mr-2 animate-spin" v-if="isSaving" />
            <Icon name="mdi:content-save" class="h-4 w-4 mr-2" v-else />
            Save Changes
          </button>
        </div>
        
        <!-- User status toggle -->
        <div v-if="user">
          <button
            v-if="user.active !== false"
            @click="toggleStatus(false)"
            class="inline-flex items-center px-3 py-2 border border-red-400/20 text-sm font-medium rounded-lg text-red-300 bg-red-500/20 hover:bg-red-500/30 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-400 transition-all duration-200 backdrop-blur-sm"
            :disabled="isStatusUpdating"
          >
            <Icon name="mdi:account-cancel" class="h-4 w-4 mr-1" />
            Deactivate
          </button>
          <button
            v-else
            @click="toggleStatus(true)"
            class="inline-flex items-center px-3 py-2 border border-green-400/20 text-sm font-medium rounded-lg text-green-300 bg-green-500/20 hover:bg-green-500/30 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-400 transition-all duration-200 backdrop-blur-sm"
            :disabled="isStatusUpdating"
          >
            <Icon name="mdi:account-check" class="h-4 w-4 mr-1" />
            Activate
          </button>
        </div>
      </div>
    </div>
    
    <div class="px-6 py-5" v-if="isLoading">
      <div class="flex justify-center py-12">
        <Icon name="mdi:loading" class="h-8 w-8 text-blue-400 animate-spin" />
      </div>
    </div>
    
    <div v-else-if="user" class="border-t border-white/10">
      <dl>
        <!-- Basic Information Section -->
        <div class="bg-gray-800/30 px-6 py-4 border-b border-white/10">
          <dt class="text-sm font-medium text-gray-300 flex items-center">
            <Icon name="mdi:account-details" class="h-5 w-5 mr-2 text-gray-400" />
            Basic Information
          </dt>
          <dd class="mt-1 text-sm text-gray-400"></dd>
        </div>
        
        <!-- Avatar -->
        <div class="bg-gray-800/20 px-6 py-5 grid grid-cols-1 sm:grid-cols-3 gap-4 border-b border-white/10">
          <dt class="text-sm font-medium text-gray-300">Profile Photo</dt>
          <dd class="mt-1 text-sm text-gray-300 sm:mt-0 sm:col-span-2">
            <div class="flex items-center">
              <div class="relative">
                <img 
                  :src="profilePhotoUrl" 
                  alt="Profile photo" 
                  class="h-16 w-16 rounded-full object-cover border-2 border-white/20"
                />
                
                <span 
                  class="absolute bottom-0 right-0 h-3.5 w-3.5 rounded-full border-2 border-gray-800" 
                  :class="user.active !== false ? 'bg-green-400' : 'bg-red-400'"
                ></span>
              </div>
              
              <div v-if="isEditing" class="ml-4">
                <button 
                  type="button"
                  class="bg-gray-700/50 py-2 px-3 border border-white/20 rounded-lg shadow-sm text-sm leading-4 font-medium text-gray-300 hover:bg-gray-600/50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-400 transition-all duration-200 backdrop-blur-sm"
                >
                  Change
                </button>
              </div>
            </div>
          </dd>
        </div>
        
        <!-- Name -->
        <div class="bg-gray-800/20 px-6 py-5 grid grid-cols-1 sm:grid-cols-3 gap-4 border-b border-white/10">
          <dt class="text-sm font-medium text-gray-300">Full name</dt>
          <dd class="mt-1 text-sm text-gray-300 sm:mt-0 sm:col-span-2">
            <template v-if="!isEditing">
              {{ fullName }}
            </template>
            <div v-else class="flex flex-col space-y-2 sm:flex-row sm:space-y-0 sm:space-x-2">
              <input
                type="text"
                v-model="editForm.first_name"
                placeholder="First name"
                class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-white/20 rounded-md bg-gray-700/50 text-gray-200 placeholder-gray-400 backdrop-blur-sm"
              />
              <input
                type="text"
                v-model="editForm.last_name"
                placeholder="Last name"
                class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-white/20 rounded-md bg-gray-700/50 text-gray-200 placeholder-gray-400 backdrop-blur-sm"
              />
            </div>
          </dd>
        </div>

        <!-- Email -->
        <div class="bg-gray-800/20 px-6 py-5 grid grid-cols-1 sm:grid-cols-3 gap-4 border-b border-white/10">
          <dt class="text-sm font-medium text-gray-300">Email address</dt>
          <dd class="mt-1 text-sm text-gray-300 sm:mt-0 sm:col-span-2">
            {{ user.email }}
            <span v-if="user.email_verified" class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-500/20 text-green-300 border border-green-400/20">
              <Icon name="mdi:check-circle" class="h-3 w-3 mr-1" />
              Verified
            </span>
            <span v-else class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-500/20 text-yellow-300 border border-yellow-400/20">
              <Icon name="mdi:alert-circle" class="h-3 w-3 mr-1" />
              Not verified
            </span>
          </dd>
        </div>

        <!-- Phone -->
        <div class="bg-gray-800/20 px-6 py-5 grid grid-cols-1 sm:grid-cols-3 gap-4 border-b border-white/10">
          <dt class="text-sm font-medium text-gray-300">Phone number</dt>
          <dd class="mt-1 text-sm text-gray-300 sm:mt-0 sm:col-span-2">
            <template v-if="!isEditing">
              {{ user.phone || 'Not provided' }}
            </template>
            <input
              v-else
              type="tel"
              v-model="editForm.phone"
              placeholder="Phone number"
              class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-white/20 rounded-md bg-gray-700/50 text-gray-200 placeholder-gray-400 backdrop-blur-sm"
            />
          </dd>
        </div>
        
        <!-- Account Information Section -->
        <div class="bg-gray-800/30 px-6 py-4 border-b border-white/10">
          <dt class="text-sm font-medium text-gray-300 flex items-center">
            <Icon name="mdi:shield-account" class="h-5 w-5 mr-2 text-gray-400" />
            Account Information
          </dt>
          <dd class="mt-1 text-sm text-gray-400"></dd>
        </div>

        <!-- Roles -->
        <div class="bg-gray-800/20 px-6 py-5 grid grid-cols-1 sm:grid-cols-3 gap-4 border-b border-white/10">
          <dt class="text-sm font-medium text-gray-300">Roles</dt>
          <dd class="mt-1 text-sm text-gray-300 sm:mt-0 sm:col-span-2">
            <div class="flex flex-wrap gap-1.5">
              <span
                v-for="role in userRoles"
                :key="role"
                :class="getRoleBadgeClass(role)"
              >
                {{ role }}
              </span>
              <span v-if="userRoles.length === 0" class="text-gray-400">No roles assigned</span>
            </div>
          </dd>
        </div>

        <!-- Account Created -->
        <div class="bg-gray-800/20 px-6 py-5 grid grid-cols-1 sm:grid-cols-3 gap-4 border-b border-white/10">
          <dt class="text-sm font-medium text-gray-300">Account created</dt>
          <dd class="mt-1 text-sm text-gray-300 sm:mt-0 sm:col-span-2">
            {{ formatDate(user.created_at || user.created_date || user.createdAt) }}
          </dd>
        </div>

        <!-- Last Login -->
        <div class="bg-gray-800/20 px-6 py-5 grid grid-cols-1 sm:grid-cols-3 gap-4 border-b border-white/10">
          <dt class="text-sm font-medium text-gray-300">Last login</dt>
          <dd class="mt-1 text-sm text-gray-300 sm:mt-0 sm:col-span-2">
            {{ lastLoginDate ? formatDate(lastLoginDate) : 'Never' }}
          </dd>
        </div>
        
        <!-- Additional Information Section -->
        <div class="bg-gray-800/30 px-6 py-4 border-b border-white/10">
          <dt class="text-sm font-medium text-gray-300 flex items-center">
            <Icon name="mdi:text-box" class="h-5 w-5 mr-2 text-gray-400" />
            Additional Information
          </dt>
          <dd class="mt-1 text-sm text-gray-400"></dd>
        </div>

        <!-- Custom fields - dynamically rendered -->
        <template v-for="(value, key) in customFields" :key="key">
          <div class="bg-gray-800/20 px-6 py-5 grid grid-cols-1 sm:grid-cols-3 gap-4 border-b border-white/10">
            <dt class="text-sm font-medium text-gray-300">{{ formatFieldName(key) }}</dt>
            <dd class="mt-1 text-sm text-gray-300 sm:mt-0 sm:col-span-2">
              <template v-if="!isEditing">
                {{ formatFieldValue(value) }}
              </template>
              <input
                v-else
                type="text"
                v-model="editForm.customFields[key]"
                :placeholder="formatFieldName(key)"
                class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-white/20 rounded-md bg-gray-700/50 text-gray-200 placeholder-gray-400 backdrop-blur-sm"
              />
            </dd>
          </div>
        </template>

        <!-- User Activity Section -->
        <div v-if="showActivity" class="bg-gray-800/30 px-6 py-4 border-b border-white/10">
          <dt class="text-sm font-medium text-gray-300 flex items-center justify-between">
            <div class="flex items-center">
              <Icon name="mdi:history" class="h-5 w-5 mr-2 text-gray-400" />
              Recent Activity
            </div>
            <button
              @click="loadMoreActivity"
              v-if="activityLogs.length > 0"
              class="text-xs text-blue-400 hover:text-blue-300 mr-4 sm:hidden transition-colors duration-200"
            >
              View more
            </button>
          </dt>
          <dd class="mt-1 text-sm text-gray-400 sm:mt-0 sm:col-span-2 flex justify-between items-center">
            <div></div>
            <button
              @click="loadMoreActivity"
              v-if="activityLogs.length > 0"
              class="text-xs text-blue-400 hover:text-blue-300 hidden sm:block transition-colors duration-200"
            >
              View more
            </button>
          </dd>
        </div>
        
        <!-- Activity logs -->
        <div v-if="showActivity" class="bg-gray-800/20 px-6 py-5 border-b border-white/10">
          <div v-if="isLoadingActivity" class="flex justify-center py-4">
            <Icon name="mdi:loading" class="h-5 w-5 text-blue-400 animate-spin" />
          </div>

          <div v-else-if="activityLogs.length > 0" class="flow-root">
            <ul class="-mb-8">
              <li v-for="(activity, idx) in activityLogs" :key="idx">
                <div class="relative pb-8">
                  <span v-if="idx !== activityLogs.length - 1" class="absolute top-4 left-4 -ml-px h-full w-0.5 bg-white/20" aria-hidden="true"></span>
                  <div class="relative flex space-x-3">
                    <div>
                      <span
                        class="h-8 w-8 rounded-full flex items-center justify-center ring-8 ring-gray-800/50 backdrop-blur-sm"
                        :class="{
                          'bg-green-500/20 border border-green-400/20': activity.type === 'login',
                          'bg-blue-500/20 border border-blue-400/20': activity.type === 'activity',
                          'bg-purple-500/20 border border-purple-400/20': activity.type === 'admin_action'
                        }"
                      >
                        <Icon
                          :name="
                            activity.type === 'login' ? 'mdi:login' :
                            activity.type === 'activity' ? 'mdi:clipboard-text' :
                            'mdi:shield-account'
                          "
                          :class="{
                            'text-green-400': activity.type === 'login',
                            'text-blue-400': activity.type === 'activity',
                            'text-purple-400': activity.type === 'admin_action'
                          }"
                          class="h-5 w-5"
                        />
                      </span>
                    </div>
                    <div class="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4">
                      <div>
                        <p class="text-sm text-gray-300">
                          {{ formatActivityDescription(activity) }}
                        </p>
                      </div>
                      <div class="text-right text-sm whitespace-nowrap text-gray-400">
                        {{ formatDate(activity.timestamp) }}
                      </div>
                    </div>
                  </div>
                </div>
              </li>
            </ul>
          </div>

          <div v-else class="text-sm text-gray-400 text-center py-4">
            No recent activity found
          </div>
        </div>
      </dl>
    </div>
    
    <div v-else-if="error" class="border-t border-white/10 p-6">
      <div class="bg-red-500/20 border-l-4 border-red-400 p-4 rounded-lg backdrop-blur-sm">
        <div class="flex">
          <div class="flex-shrink-0">
            <Icon name="mdi:alert-circle" class="h-5 w-5 text-red-400" />
          </div>
          <div class="ml-3">
            <p class="text-sm text-red-300">
              {{ error }}
            </p>
          </div>
        </div>
      </div>
    </div>

    <div v-else class="border-t border-white/10 p-6">
      <div class="bg-yellow-500/20 border-l-4 border-yellow-400 p-4 rounded-lg backdrop-blur-sm">
        <div class="flex">
          <div class="flex-shrink-0">
            <Icon name="mdi:alert" class="h-5 w-5 text-yellow-400" />
          </div>
          <div class="ml-3">
            <p class="text-sm text-yellow-300">
              User not found
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import moment from 'moment'

const props = defineProps({
  userId: {
    type: String,
    required: true
  },
  showActivity: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['update', 'status-change'])

// State
const user = ref<any>(null)
const isLoading = ref(true)
const error = ref('')
const isEditing = ref(false)
const isSaving = ref(false)
const isStatusUpdating = ref(false)

// Activity logs
const activityLogs = ref<any[]>([])
const isLoadingActivity = ref(false)
const activityPage = ref(1)
const activityLimit = ref(5)

// Edit form
const editForm = ref({
  first_name: '',
  last_name: '',
  phone: '',
  customFields: {} as Record<string, any>
})

// Fetch user data
const fetchUserData = async () => {
  isLoading.value = true
  error.value = ''
  
  try {
    const { getUserById } = useUserManagement()
    const userData = await getUserById(props.userId)
    
    if (userData) {
      user.value = userData
      initEditForm()
      
      // If showing activity, load it
      if (props.showActivity) {
        loadActivityLogs()
      }
    } else {
      error.value = 'User not found'
    }
  } catch (err: any) {
    console.error('Error fetching user data:', err)
    error.value = err.message || 'Failed to load user data'
  } finally {
    isLoading.value = false
  }
}

// Load user activity logs
const loadActivityLogs = async () => {
  if (!props.userId) return
  
  isLoadingActivity.value = true
  
  try {
    const { getUserActivityLogs } = useUserManagement()
    const logs = await getUserActivityLogs(props.userId)
    
    // Get limited number of logs based on page
    activityLogs.value = logs.slice(0, activityPage.value * activityLimit.value)
  } catch (err) {
    console.error('Error loading activity logs:', err)
  } finally {
    isLoadingActivity.value = false
  }
}

const loadMoreActivity = () => {
  activityPage.value++
  loadActivityLogs()
}

// Initialize edit form from user data
const initEditForm = () => {
  if (!user.value) return
  
  editForm.value = {
    first_name: user.value.first_name || '',
    last_name: user.value.last_name || '',
    phone: user.value.phone || '',
    customFields: {} as Record<string, any>
  }
  
  // Add custom fields
  Object.keys(user.value).forEach(key => {
    // Skip standard fields
    if (!['id', 'uid', 'email', 'first_name', 'last_name', 'phone', 'roles', 'active', 
         'email_verified', 'created_at', 'updated_at', 'created_date', 'updated_date', 
         'createdAt', 'updatedAt'].includes(key)) {
      editForm.value.customFields[key] = user.value[key]
    }
  })
}

// Profile actions
const startEditing = () => {
  initEditForm()
  isEditing.value = true
}

const cancelEditing = () => {
  isEditing.value = false
}

const saveChanges = async () => {
  if (!user.value) return
  
  isSaving.value = true
  
  try {
    const { updateUserProfile } = useUserManagement()
    
    // Prepare data for update
    const profileData = {
      first_name: editForm.value.first_name,
      last_name: editForm.value.last_name,
      phone: editForm.value.phone,
      ...editForm.value.customFields
    }
    
    const result = await updateUserProfile(props.userId, profileData)
    
    if (result.success) {
      // Update local user data
      Object.assign(user.value, profileData)
      isEditing.value = false
      emit('update', user.value)
    } else {
      error.value = result.error || 'Failed to update profile'
    }
  } catch (err: any) {
    console.error('Error updating profile:', err)
    error.value = err.message || 'Failed to update profile'
  } finally {
    isSaving.value = false
  }
}

// Toggle user status (active/inactive)
const toggleStatus = async (makeActive: boolean) => {
  if (!user.value) return
  
  isStatusUpdating.value = true
  
  try {
    const { toggleUserStatus } = useUserManagement()
    const result = await toggleUserStatus(props.userId, makeActive)
    
    if (result.success) {
      // Update local user status
      user.value.active = makeActive
      emit('status-change', { userId: props.userId, active: makeActive })
    } else {
      error.value = result.error || `Failed to ${makeActive ? 'activate' : 'deactivate'} user`
    }
  } catch (err: any) {
    console.error('Error updating user status:', err)
    error.value = err.message || `Failed to ${makeActive ? 'activate' : 'deactivate'} user`
  } finally {
    isStatusUpdating.value = false
  }
}

// Computed properties
const fullName = computed(() => {
  if (!user.value) return ''
  
  const firstName = user.value.first_name || ''
  const lastName = user.value.last_name || ''
  
  if (firstName || lastName) {
    return `${firstName} ${lastName}`.trim()
  }
  
  return user.value.email ? user.value.email.split('@')[0] : 'Unnamed User'
})

const profilePhotoUrl = computed(() => {
  if (user.value?.photoURL || user.value?.profile_photo) {
    return user.value.photoURL || user.value.profile_photo
  }
  
  // Generate initials-based avatar or use default avatar
  return `https://ui-avatars.com/api/?name=${encodeURIComponent(fullName.value)}&background=random&color=fff`
})

const userRoles = computed(() => {
  if (!user.value) return []
  
  if (user.value.roles && Array.isArray(user.value.roles)) {
    return user.value.roles
  } else if (user.value.role) {
    return [user.value.role]
  }
  
  return ['User'] // Default role
})

const customFields = computed(() => {
  if (!user.value) return {}
  
  const result: Record<string, any> = {}
  
  Object.entries(user.value).forEach(([key, value]) => {
    // Skip standard fields and empty/null values
    if (!['id', 'uid', 'email', 'first_name', 'last_name', 'phone', 'roles', 'active', 
         'email_verified', 'created_at', 'updated_at', 'created_date', 'updated_date', 
         'createdAt', 'updatedAt', 'photoURL', 'profile_photo'].includes(key) && 
        value !== null && value !== undefined && value !== '') {
      result[key] = value
    }
  })
  
  return result
})

const lastLoginDate = computed(() => {
  if (activityLogs.value.length === 0) return null
  
  const loginLogs = activityLogs.value.filter(log => log.type === 'login')
  return loginLogs.length > 0 ? loginLogs[0].timestamp : null
})

// Helper functions
const formatDate = (date: any) => {
  if (!date) return 'N/A'
  return moment(date).format('MMM D, YYYY, h:mm A')
}

const formatFieldName = (key: string) => {
  return key
    .replace(/_/g, ' ')
    .split(' ')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ')
}

const formatFieldValue = (value: any) => {
  if (value === null || value === undefined) return 'N/A'
  
  if (Array.isArray(value)) {
    return value.join(', ')
  } else if (typeof value === 'object' && value !== null) {
    return JSON.stringify(value)
  } else if (typeof value === 'boolean') {
    return value ? 'Yes' : 'No'
  }
  
  return value.toString()
}

const formatActivityDescription = (activity: any) => {
  switch (activity.type) {
    case 'login':
      return `Logged in${activity.device ? ` from ${activity.device}` : ''}`
    case 'admin_action':
      return `Admin ${activity.performedByEmail || 'Unknown'} ${activity.action.replace(/_/g, ' ')} for this user`
    case 'activity':
      return activity.description || 'Performed an action'
    default:
      return 'Unknown activity'
  }
}

const getRoleBadgeClass = (role: string) => {
  const baseClass = "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border backdrop-blur-sm"

  switch (role) {
    case 'Admin':
      return `${baseClass} bg-red-500/20 text-red-300 border-red-400/20`
    case 'Editor':
      return `${baseClass} bg-green-500/20 text-green-300 border-green-400/20`
    case 'Moderator':
      return `${baseClass} bg-orange-500/20 text-orange-300 border-orange-400/20`
    case 'User':
      return `${baseClass} bg-blue-500/20 text-blue-300 border-blue-400/20`
    default:
      return `${baseClass} bg-gray-500/20 text-gray-300 border-gray-400/20`
  }
}

// Watchers
watch(() => props.userId, (newId, oldId) => {
  if (newId && newId !== oldId) {
    fetchUserData()
  }
})

// Initialize
onMounted(() => {
  if (props.userId) {
    fetchUserData()
  }
})
</script>