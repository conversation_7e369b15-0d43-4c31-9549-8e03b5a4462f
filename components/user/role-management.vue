<template>
  <div>
    <!-- Simple version for single user view -->
    <div v-if="!multipleUsers" class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
      <div class="px-4 py-3 bg-gray-50 border-b border-gray-200 flex justify-between items-center">
        <h3 class="text-sm font-medium text-gray-700">User Roles</h3>
        <NuxtLink
          to="/c/admin/roles"
          class="text-xs text-blue-600 hover:text-blue-800 flex items-center"
          v-if="hasRole('Admin')"
        >
          <Icon name="mdi:cog" class="h-3 w-3 mr-1" />
          Manage Roles
        </NuxtLink>
      </div>

      <div class="p-4 space-y-4">
        <div v-if="loadingRoles" class="py-4 flex justify-center">
          <Icon name="mdi:loading" class="h-5 w-5 text-indigo-600 animate-spin" />
        </div>

        <template v-else>
          <div v-for="role in availableRoles" :key="role.id" class="flex items-center justify-between">
            <div class="flex items-center">
              <input
                type="checkbox"
                :id="`role-${role.id}`"
                v-model="userRoles"
                :value="role.id"
                @change="handleRoleChange(role.id)"
                :disabled="isDefaultRole(role.id) || disabled"
                class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
              />
              <label :for="`role-${role.id}`" class="ml-2 text-sm text-gray-700 flex items-center">
                <div
                  class="w-2 h-2 rounded-full mr-2"
                  :style="{ backgroundColor: role.color || '#3B82F6' }"
                ></div>
                {{ role.name }}
              </label>
            </div>
            <div class="text-xs text-gray-500">
              {{ role.description }}
            </div>
          </div>

          <div v-if="availableRoles.length === 0" class="text-sm text-gray-500">
            No roles available.
          </div>

          <p v-if="errorMessage" class="text-sm text-red-600 mt-1">{{ errorMessage }}</p>

          <div v-if="showSaveButton" class="flex justify-end pt-2">
            <button
              @click="saveChanges"
              :disabled="!hasChanges || savingChanges"
              :class="[
                'inline-flex items-center px-3 py-1.5 border rounded-md text-sm font-medium',
                (!hasChanges || savingChanges)
                  ? 'bg-gray-100 text-gray-400 border-gray-300 cursor-not-allowed'
                  : 'bg-indigo-600 text-white border-transparent hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500'
              ]"
            >
              <Icon name="mdi:content-save" class="h-4 w-4 mr-1" v-if="!savingChanges" />
              <Icon name="mdi:loading" class="h-4 w-4 mr-1 animate-spin" v-else />
              Save Changes
            </button>
          </div>
        </template>
      </div>
    </div>

    <!-- Advanced version for role management page -->
    <div v-else class="bg-white rounded-lg shadow overflow-hidden">
      <div class="px-4 py-5 border-b border-gray-200 sm:px-6 flex justify-between items-center">
        <div>
          <h3 class="text-lg leading-6 font-medium text-gray-900">Role Management</h3>
          <p class="mt-1 text-sm text-gray-500">Manage user roles and permissions</p>
        </div>

        <button
          @click="createNewRole"
          class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
        >
          <Icon name="mdi:shield-plus" class="mr-1 h-4 w-4" />
          Create New Role
        </button>
      </div>

      <div v-if="loadingRoles" class="py-12 flex justify-center">
        <Icon name="mdi:loading" class="h-8 w-8 text-indigo-600 animate-spin" />
      </div>

      <div v-else class="px-4 py-5 sm:p-6">
        <!-- Role list -->
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Role
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Description
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Users
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Default
                </th>
                <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr v-for="role in availableRoles" :key="role.id">
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="flex items-center">
                    <div
                      class="flex-shrink-0 h-3 w-3 rounded-full"
                      :style="{ backgroundColor: role.color || '#3B82F6' }"
                    ></div>
                    <div class="ml-3">
                      <div class="text-sm font-medium text-gray-900">{{ role.name }}</div>
                    </div>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-500">{{ role.description }}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-900">{{ role.userCount || 0 }}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-500">
                    <span v-if="isDefaultRole(role.id)" class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                      Default
                    </span>
                    <span v-else>-</span>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <div class="flex justify-end">
                    <button
                      @click="editRole(role)"
                      class="text-indigo-600 hover:text-indigo-900 mr-4"
                    >
                      Edit
                    </button>
                    <button
                      @click="confirmDeleteRole(role)"
                      class="text-red-600 hover:text-red-900"
                      :disabled="isDefaultRole(role.id)"
                      :class="{ 'opacity-50 cursor-not-allowed': isDefaultRole(role.id) }"
                    >
                      Delete
                    </button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <!-- Role edit/create modal -->
    <div
      v-if="showRoleModal"
      class="fixed inset-0 bg-gray-500 bg-opacity-50 flex items-center justify-center z-50"
    >
      <div class="bg-white rounded-lg shadow-xl max-w-lg w-full mx-4">
        <div class="px-6 py-4 border-b border-gray-200">
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-medium text-gray-900">
              {{ editingRole ? 'Edit Role' : 'Create New Role' }}
            </h3>
            <button @click="showRoleModal = false" class="text-gray-400 hover:text-gray-500">
              <Icon name="mdi:close" class="h-5 w-5" />
            </button>
          </div>
        </div>

        <div class="px-6 py-4">
          <div class="space-y-4">
            <div>
              <label for="role-name" class="block text-sm font-medium text-gray-700">Role Name</label>
              <input
                type="text"
                id="role-name"
                v-model="roleForm.name"
                class="mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"
              />
            </div>

            <div>
              <label for="role-id" class="block text-sm font-medium text-gray-700">
                Role ID
                <span class="text-xs text-gray-500 font-normal">(unique identifier, no spaces)</span>
              </label>
              <input
                type="text"
                id="role-id"
                v-model="roleForm.id"
                :disabled="editingRole"
                class="mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"
                :class="{ 'bg-gray-100': editingRole }"
              />
            </div>

            <div>
              <label for="role-description" class="block text-sm font-medium text-gray-700">Description</label>
              <textarea
                id="role-description"
                v-model="roleForm.description"
                rows="3"
                class="mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"
              ></textarea>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700">Role Color</label>
              <div class="mt-1 flex space-x-2">
                <button
                  v-for="color in roleColors"
                  :key="color.value"
                  @click="roleForm.color = color.value"
                  class="w-6 h-6 rounded-full border-2"
                  :class="[
                    color.bgClass,
                    roleForm.color === color.value ? 'ring-2 ring-offset-2 ring-gray-400' : 'border-transparent'
                  ]"
                ></button>
              </div>
            </div>

            <div class="flex items-start">
              <div class="flex items-center h-5">
                <input
                  id="role-default"
                  type="checkbox"
                  v-model="roleForm.isDefault"
                  class="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300 rounded"
                />
              </div>
              <div class="ml-3 text-sm">
                <label for="role-default" class="font-medium text-gray-700">Default Role</label>
                <p class="text-gray-500">This role will be automatically assigned to new users</p>
              </div>
            </div>

            <div v-if="roleError" class="text-sm text-red-600">
              {{ roleError }}
            </div>
          </div>
        </div>

        <div class="px-6 py-4 border-t border-gray-200 flex justify-end space-x-3">
          <button
            @click="showRoleModal = false"
            class="inline-flex justify-center py-2 px-4 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            Cancel
          </button>
          <button
            @click="saveRole"
            :disabled="!isRoleFormValid || savingRole"
            :class="[
              'inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500',
              !isRoleFormValid || savingRole ? 'bg-indigo-400 cursor-not-allowed' : 'bg-indigo-600 hover:bg-indigo-700'
            ]"
          >
            <Icon name="mdi:loading" class="h-4 w-4 mr-2 animate-spin" v-if="savingRole" />
            {{ editingRole ? 'Update Role' : 'Create Role' }}
          </button>
        </div>
      </div>
    </div>

    <!-- Delete confirmation modal -->
    <div
      v-if="showDeleteConfirmation"
      class="fixed inset-0 bg-gray-500 bg-opacity-50 flex items-center justify-center z-50"
    >
      <div class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
        <div class="p-6">
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-medium text-gray-900">Delete Role</h3>
            <button @click="showDeleteConfirmation = false" class="text-gray-400 hover:text-gray-500">
              <Icon name="mdi:close" class="h-5 w-5" />
            </button>
          </div>

          <div class="mb-6">
            <p class="text-sm text-gray-600">
              Are you sure you want to delete the role "{{ roleToDelete?.name || '' }}"?
              <template v-if="roleToDelete?.userCount > 0">
                This will remove the role from {{ roleToDelete?.userCount }} user(s).
              </template>
            </p>
            <div class="mt-2 p-2 bg-yellow-50 border border-yellow-100 rounded-md">
              <p class="text-xs text-yellow-800 flex items-start">
                <Icon name="mdi:alert" class="mr-1 h-4 w-4 flex-shrink-0 mt-0.5" />
                <span>This action cannot be undone.</span>
              </p>
            </div>
          </div>

          <div class="flex items-center justify-end space-x-3">
            <button
              @click="showDeleteConfirmation = false"
              class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
              Cancel
            </button>
            <button
              @click="deleteRole"
              :disabled="deletingRole"
              class="px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
            >
              <span v-if="deletingRole">
                <Icon name="mdi:loading" class="h-4 w-4 mr-1 animate-spin inline" />
                Deleting...
              </span>
              <span v-else>
                Delete Role
              </span>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { usePermissions } from '~/composables/permissions'
import { hasRole } from '~/composables/info'
import { database } from '~/composables/database'

const props = defineProps({
  // User ID to edit roles for (in single user mode)
  userId: {
    type: String,
    default: null
  },
  // Current roles of the user (in single user mode)
  initialRoles: {
    type: Array,
    default: () => []
  },
  // Whether to show in multi-user management mode
  multipleUsers: {
    type: Boolean,
    default: false
  },
  // Whether to show a save button in single user mode (false to save automatically)
  showSaveButton: {
    type: Boolean,
    default: false
  },
  // Whether the component is disabled
  disabled: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['role-change', 'save', 'role-saved', 'role-deleted'])

// Get permissions composable
const { roles: permissionRoles, fetchRoles } = usePermissions()

// Role state
const availableRoles = ref<any[]>([])

const userRoles = ref<string[]>([...(props.initialRoles as string[])])
const originalRoles = ref<string[]>([...(props.initialRoles as string[])])
const loadingRoles = ref(false)
const savingChanges = ref(false)
const errorMessage = ref('')

// Role editor state
const showRoleModal = ref(false)
const editingRole = ref(false)
const savingRole = ref(false)
const roleError = ref('')
const roleForm = ref({
  id: '',
  name: '',
  description: '',
  color: 'blue',
  isDefault: false
})

// Delete role state
const showDeleteConfirmation = ref(false)
const deletingRole = ref(false)
const roleToDelete = ref<any>(null)

// Role colors for selection
const roleColors = [
  { value: 'blue', bgClass: 'bg-blue-500' },
  { value: 'red', bgClass: 'bg-red-500' },
  { value: 'green', bgClass: 'bg-green-500' },
  { value: 'yellow', bgClass: 'bg-yellow-500' },
  { value: 'purple', bgClass: 'bg-purple-500' },
  { value: 'pink', bgClass: 'bg-pink-500' },
  { value: 'indigo', bgClass: 'bg-indigo-500' },
  { value: 'teal', bgClass: 'bg-teal-500' },
  { value: 'orange', bgClass: 'bg-orange-500' },
  { value: 'gray', bgClass: 'bg-gray-500' }
]

// Computed properties
const hasChanges = computed(() => {
  if (userRoles.value.length !== originalRoles.value.length) return true
  return userRoles.value.some(role => !originalRoles.value.includes(role)) ||
    originalRoles.value.some(role => !userRoles.value.includes(role))
})

const isRoleFormValid = computed(() => {
  return (
    roleForm.value.id?.trim().length > 0 &&
    roleForm.value.name?.trim().length > 0 &&
    !/\s/.test(roleForm.value.id)
  )
})

// Role modification functions for single user mode
const handleRoleChange = (roleId: string) => {
  const isAdmin = roleId === 'Admin'

  // For automatic save mode
  if (!props.showSaveButton) {
    saveRoleChanges()
  }

  emit('role-change', {
    roles: [...userRoles.value],
    added: userRoles.value.includes(roleId),
    roleId,
    isAdmin
  })
}

const saveChanges = async () => {
  await saveRoleChanges()
}

const saveRoleChanges = async () => {
  if (!props.userId || !hasChanges.value) return

  savingChanges.value = true
  errorMessage.value = ''

  try {
    // Update the user's roles in the database
    const { updateToId } = database()
    await updateToId(props.userId, 'user', { roles: userRoles.value })

    // Update original roles to reflect the new state
    originalRoles.value = [...userRoles.value]

    emit('save', userRoles.value)
  } catch (error) {
    console.error('Error updating user roles:', error)
    errorMessage.value = 'Failed to update roles. Please try again.'
  } finally {
    savingChanges.value = false
  }
}

// Role management functions (for multi-user mode)
const fetchRoleData = async () => {
  loadingRoles.value = true

  try {
    // For now, using local data
    // In a real implementation, we would fetch this data from Firestore

    // Count users with each role
    const { getCollection } = database()
    const users = await getCollection('user')

    const roleCounts: Record<string, number> = {}

    users.forEach((user: any) => {
      if (user.roles && Array.isArray(user.roles)) {
        user.roles.forEach((role: string) => {
          if (!roleCounts[role]) {
            roleCounts[role] = 0
          }
          roleCounts[role]++
        })
      }
    })

    // Update available roles with user counts
    availableRoles.value = availableRoles.value.map(role => ({
      ...role,
      userCount: roleCounts[role.id] || 0
    }))
  } catch (error) {
    console.error('Error fetching role data:', error)
  } finally {
    loadingRoles.value = false
  }
}

// Role creation/editing functions
const createNewRole = () => {
  editingRole.value = false
  roleForm.value = {
    id: '',
    name: '',
    description: '',
    color: 'blue',
    isDefault: false
  }
  roleError.value = ''
  showRoleModal.value = true
}

const editRole = (role: any) => {
  editingRole.value = true
  roleForm.value = { ...role }
  roleError.value = ''
  showRoleModal.value = true
}

const saveRole = async () => {
  if (!isRoleFormValid.value) return

  savingRole.value = true
  roleError.value = ''

  try {
    const roleId = roleForm.value.id

    if (!editingRole.value) {
      // Check if role ID already exists
      const existingRole = availableRoles.value.find(r => r.id === roleId)
      if (existingRole) {
        roleError.value = 'A role with this ID already exists'
        savingRole.value = false
        return
      }

      // Create new role
      const newRole = { ...roleForm.value, userCount: 0 }
      availableRoles.value.push(newRole)

      // In a real implementation, save the role to Firestore

      emit('role-saved', newRole)
    } else {
      // Update existing role
      const roleIndex = availableRoles.value.findIndex(r => r.id === roleId)
      if (roleIndex >= 0) {
        // Preserve user count when updating
        const userCount = availableRoles.value[roleIndex].userCount
        availableRoles.value[roleIndex] = { ...roleForm.value, userCount }

        // In a real implementation, update the role in Firestore

        emit('role-saved', availableRoles.value[roleIndex])
      }
    }

    // Handle default role logic
    if (roleForm.value.isDefault) {
      // Ensure only one default role
      availableRoles.value.forEach(r => {
        if (r.id !== roleId) {
          r.isDefault = false
        }
      })
    }

    showRoleModal.value = false
  } catch (error) {
    console.error('Error saving role:', error)
    roleError.value = 'Failed to save role. Please try again.'
  } finally {
    savingRole.value = false
  }
}

// Role deletion
const confirmDeleteRole = (role: any) => {
  if (isDefaultRole(role.id)) return

  roleToDelete.value = role
  showDeleteConfirmation.value = true
}

const deleteRole = async () => {
  if (!roleToDelete.value) return

  deletingRole.value = true

  try {
    const roleId = roleToDelete.value.id

    // Remove role from available roles
    availableRoles.value = availableRoles.value.filter(r => r.id !== roleId)

    // In a real implementation, we would:
    // 1. Delete the role from Firestore
    // 2. Remove this role from all users who have it

    emit('role-deleted', roleToDelete.value)

    showDeleteConfirmation.value = false
  } catch (error) {
    console.error('Error deleting role:', error)
  } finally {
    deletingRole.value = false
    roleToDelete.value = null
  }
}

// Helper functions
const isDefaultRole = (roleId: string) => {
  const role = availableRoles.value.find(r => r.id === roleId)
  return role && role.isDefault
}

// Watch for external role changes
watch(() => props.initialRoles, (newRoles) => {
  userRoles.value = [...(newRoles as string[])]
  originalRoles.value = [...(newRoles as string[])]
}, { deep: true })

// Initialize component
onMounted(async () => {
  // Fetch roles from the permissions system
  await fetchRoles()

  // Convert permission roles to the format expected by this component
  availableRoles.value = permissionRoles.value.map(role => ({
    id: role.id,
    name: role.name,
    description: role.description,
    color: role.color,
    isDefault: role.isDefault
  }))

  // If in multi-user mode, fetch additional role data
  if (props.multipleUsers) {
    fetchRoleData()
  }
})
</script>