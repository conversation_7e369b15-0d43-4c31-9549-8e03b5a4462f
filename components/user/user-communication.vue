<template>
  <div class="bg-gradient-to-br from-gray-800/50 via-gray-700/50 to-gray-800/50 backdrop-blur-sm rounded-xl border border-white/10 overflow-hidden">
    <!-- Component Header -->
    <div class="px-6 py-5 bg-gray-800/30 border-b border-white/10 flex justify-between items-center">
      <h3 class="text-lg font-medium text-white">User Communication</h3>
      <div class="flex space-x-2">
        <button
          @click="activeTab = 'compose'"
          :class="[
            'px-3 py-2 text-sm rounded-lg transition-all duration-200 backdrop-blur-sm',
            activeTab === 'compose'
              ? 'bg-blue-600 text-white shadow-lg'
              : 'bg-gray-700/50 text-gray-300 hover:bg-gray-600/50 border border-white/20'
          ]"
        >
          Compose
        </button>
        <button
          @click="activeTab = 'history'"
          :class="[
            'px-3 py-2 text-sm rounded-lg transition-all duration-200 backdrop-blur-sm',
            activeTab === 'history'
              ? 'bg-blue-600 text-white shadow-lg'
              : 'bg-gray-700/50 text-gray-300 hover:bg-gray-600/50 border border-white/20'
          ]"
        >
          History
        </button>
        <button
          @click="activeTab = 'templates'"
          :class="[
            'px-3 py-2 text-sm rounded-lg transition-all duration-200 backdrop-blur-sm',
            activeTab === 'templates'
              ? 'bg-blue-600 text-white shadow-lg'
              : 'bg-gray-700/50 text-gray-300 hover:bg-gray-600/50 border border-white/20'
          ]"
          v-if="showTemplates"
        >
          Templates
        </button>
      </div>
    </div>
    
    <!-- Compose Tab -->
    <div v-if="activeTab === 'compose'" class="p-6">
      <form @submit.prevent="sendMessage" class="space-y-6">
        <!-- Template Selector -->
        <div v-if="templates.length > 0">
          <label class="block text-sm font-medium text-gray-300 mb-2">
            Use Template
          </label>
          <select
            v-model="selectedTemplateId"
            @change="applyTemplate"
            class="w-full px-4 py-3 border border-white/20 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-gray-700/50 text-gray-200 backdrop-blur-sm"
          >
            <option value="">No Template</option>
            <option v-for="template in templates" :key="template.id" :value="template.id">
              {{ template.name }}
            </option>
          </select>
        </div>

        <!-- Subject -->
        <div>
          <label for="subject" class="block text-sm font-medium text-gray-300 mb-2">
            Subject
          </label>
          <input
            id="subject"
            v-model="messageData.subject"
            type="text"
            class="w-full px-4 py-3 border border-white/20 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-gray-700/50 text-gray-200 placeholder-gray-400 backdrop-blur-sm"
            placeholder="Enter email subject"
            required
          />
        </div>

        <!-- Message -->
        <div>
          <label for="message" class="block text-sm font-medium text-gray-300 mb-2">
            Message
          </label>
          <textarea
            id="message"
            v-model="messageData.message"
            rows="6"
            class="w-full px-4 py-3 border border-white/20 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-gray-700/50 text-gray-200 placeholder-gray-400 backdrop-blur-sm resize-none"
            placeholder="Enter your message"
            required
          ></textarea>
        </div>

        <!-- Send Copy Checkbox -->
        <div class="flex items-center">
          <input
            id="sendCopy"
            v-model="messageData.sendCopy"
            type="checkbox"
            class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-white/20 rounded bg-gray-700/50"
          />
          <label for="sendCopy" class="ml-3 block text-sm text-gray-300">
            Send me a copy
          </label>
        </div>

        <!-- Error Message -->
        <div v-if="error" class="bg-red-500/20 border border-red-400/20 rounded-lg p-4">
          <p class="text-red-300 text-sm">{{ error }}</p>
        </div>

        <!-- Submit Button -->
        <div class="flex justify-end">
          <button
            type="submit"
            class="inline-flex items-center px-6 py-3 border border-transparent text-sm font-medium rounded-lg shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
            :disabled="sending"
          >
            <Icon v-if="sending" name="mdi:loading" class="animate-spin -ml-1 mr-2 h-4 w-4" />
            {{ sending ? 'Sending...' : 'Send Message' }}
          </button>
        </div>
      </form>
    </div>
    
    <!-- History Tab -->
    <div v-if="activeTab === 'history'" class="p-6">
      <div v-if="loading" class="py-8 flex justify-center">
        <Icon name="mdi:loading" class="h-6 w-6 text-blue-400 animate-spin" />
      </div>

      <div v-else-if="emailHistory.length === 0" class="py-8 text-center text-gray-400">
        No message history found for this user.
      </div>

      <div v-else class="space-y-4">
        <div v-for="(email, index) in emailHistory" :key="index" class="border border-white/20 rounded-lg overflow-hidden bg-gray-800/30 backdrop-blur-sm">
          <div class="px-6 py-4 bg-gray-800/50 border-b border-white/10 flex justify-between items-center">
            <div class="font-medium text-sm text-gray-200">{{ email.subject }}</div>
            <div class="text-xs text-gray-400 flex items-center space-x-2">
              <span>{{ formatDate(email.sentAt) }}</span>
              <span
                :class="[
                  'px-2 py-1 rounded-full text-xs font-medium border',
                  email.status === 'sent'
                    ? 'bg-green-500/20 text-green-300 border-green-400/20'
                    : 'bg-red-500/20 text-red-300 border-red-400/20'
                ]"
              >
                {{ email.status === 'sent' ? 'Sent' : 'Failed' }}
              </span>
            </div>
          </div>
          <div class="p-6">
            <div class="text-sm text-gray-300" v-html="email.message"></div>
            <div v-if="email.error" class="mt-3 p-3 bg-red-500/20 border border-red-400/20 rounded-lg">
              <p class="text-sm text-red-300">Error: {{ email.error }}</p>
            </div>
          </div>
        </div>

        <div v-if="hasMoreHistory" class="flex justify-center">
          <button
            @click="loadMoreHistory"
            class="inline-flex items-center px-6 py-3 border border-white/20 text-sm font-medium rounded-lg text-gray-300 bg-gray-700/50 hover:bg-gray-600/50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200 backdrop-blur-sm disabled:opacity-50 disabled:cursor-not-allowed"
            :disabled="loadingMore"
          >
            <Icon v-if="loadingMore" name="mdi:loading" class="animate-spin -ml-1 mr-2 h-4 w-4" />
            {{ loadingMore ? 'Loading...' : 'Load More' }}
          </button>
        </div>
      </div>
    </div>
    
    <!-- Templates Tab -->
    <div v-if="activeTab === 'templates' && showTemplates" class="p-6">
      <div v-if="loadingTemplates" class="py-8 flex justify-center">
        <Icon name="mdi:loading" class="h-6 w-6 text-blue-400 animate-spin" />
      </div>

      <div v-else-if="templates.length === 0" class="py-8 text-center text-gray-400">
        No email templates found. Create one to get started.
      </div>

      <div v-else class="space-y-4">
        <div v-for="template in templates" :key="template.id" class="border border-white/20 rounded-lg overflow-hidden bg-gray-800/30 backdrop-blur-sm">
          <div class="px-6 py-4 bg-gray-800/50 border-b border-white/10 flex justify-between items-center">
            <div class="font-medium text-sm text-gray-200">{{ template.name }}</div>
            <div class="flex space-x-2">
              <button
                @click="useTemplate(template)"
                class="text-xs px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200"
              >
                Use
              </button>
              <button
                @click="editTemplate(template)"
                class="text-xs px-3 py-2 bg-gray-700/50 text-gray-300 border border-white/20 rounded-lg hover:bg-gray-600/50 transition-colors duration-200"
              >
                Edit
              </button>
            </div>
          </div>
          <div class="p-6">
            <div class="text-xs text-gray-400 mb-2">Subject: {{ template.subject }}</div>
            <div class="text-sm text-gray-300" v-html="template.content"></div>
          </div>
        </div>
      </div>

      <div class="mt-6 flex justify-end">
        <button
          @click="createNewTemplate"
          class="inline-flex items-center px-6 py-3 border border-transparent text-sm font-medium rounded-lg shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200"
        >
          Create Template
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted } from 'vue'
import moment from 'moment'
import { useEmail } from '~/composables/useEmail'

// Props
const props = defineProps({
  // User email to send messages to
  userEmail: {
    type: String,
    required: true
  },
  // User ID for logging
  userId: {
    type: String,
    required: true
  },
  // Whether to show the templates tab
  showTemplates: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['message-sent', 'template-created', 'template-updated'])

// State
const activeTab = ref('compose')
const messageData = ref({
  subject: '',
  message: '',
  sendCopy: false
})
const selectedTemplateId = ref('')
const templates = ref<any[]>([])
const emailHistory = ref<any[]>([])
const error = ref('')
const sending = ref(false)
const loading = ref(false)
const loadingMore = ref(false)
const loadingTemplates = ref(false)
const historyPage = ref(1)
const historyLimit = ref(5)
const hasMoreHistory = ref(false)

// Get email composable
const { send, getEmailHistory, getEmailTemplates, getEmailTemplate } = useEmail()

// Load email history
const loadEmailHistory = async (reset = true) => {
  if (reset) {
    loading.value = true
    historyPage.value = 1
    emailHistory.value = []
  } else {
    loadingMore.value = true
    historyPage.value++
  }
  
  try {
    const limit = historyLimit.value
    const history = await getEmailHistory(props.userEmail, limit * historyPage.value)
    
    emailHistory.value = history
    
    // Check if there might be more history
    hasMoreHistory.value = history.length === limit * historyPage.value
  } catch (err) {
    console.error('Error loading email history:', err)
  } finally {
    loading.value = false
    loadingMore.value = false
  }
}

// Load more history
const loadMoreHistory = () => {
  loadEmailHistory(false)
}

// Load email templates
const loadEmailTemplates = async () => {
  loadingTemplates.value = true
  
  try {
    templates.value = await getEmailTemplates()
  } catch (err) {
    console.error('Error loading email templates:', err)
  } finally {
    loadingTemplates.value = false
  }
}

// Apply selected template
const applyTemplate = async () => {
  if (!selectedTemplateId.value) {
    messageData.value.subject = ''
    messageData.value.message = ''
    return
  }
  
  try {
    const template = await getEmailTemplate(selectedTemplateId.value)
    
    if (template) {
      messageData.value.subject = template.subject
      messageData.value.message = template.content
    }
  } catch (err) {
    console.error('Error applying template:', err)
  }
}

// Use a specific template
const useTemplate = (template: any) => {
  selectedTemplateId.value = template.id
  messageData.value.subject = template.subject
  messageData.value.message = template.content
  activeTab.value = 'compose'
}

// Edit a template
const editTemplate = (template: any) => {
  // This would open a modal or navigate to a template editor
  // For now, we'll just emit an event
  emit('template-edit', template)
}

// Create a new template
const createNewTemplate = () => {
  // This would open a modal or navigate to a template creator
  // For now, we'll just emit an event
  emit('template-create')
}

// Send a message
const sendMessage = async () => {
  if (!messageData.value.subject || !messageData.value.message) {
    error.value = 'Please provide both subject and message'
    return
  }
  
  sending.value = true
  error.value = ''
  
  try {
    const result = await send({
      to: props.userEmail,
      subject: messageData.value.subject,
      message: messageData.value.message,
      templateId: selectedTemplateId.value,
      sendCopy: messageData.value.sendCopy
    })
    
    if (result.success) {
      // Reset form
      messageData.value.subject = ''
      messageData.value.message = ''
      selectedTemplateId.value = ''
      
      // Reload history
      await loadEmailHistory()
      
      // Emit event
      emit('message-sent')
    } else {
      error.value = result.error || 'Failed to send message'
    }
  } catch (err: any) {
    error.value = err.message || 'Error sending message'
  } finally {
    sending.value = false
  }
}

// Format date
const formatDate = (date: any) => {
  return moment(date.toDate()).format('MMM D, YYYY h:mm A')
}

// Watch for tab changes
watch(activeTab, (newTab) => {
  if (newTab === 'history') {
    loadEmailHistory()
  } else if (newTab === 'templates') {
    loadEmailTemplates()
  }
})

// Load data on component mount
onMounted(() => {
  loadEmailTemplates()
})
</script>
