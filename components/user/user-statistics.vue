<template>
  <div>
    <!-- Loading state -->
    <div v-if="loading" class="py-8 flex justify-center">
      <div class="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-indigo-500"></div>
    </div>

    <!-- Error state -->
    <div v-else-if="error" class="bg-red-500/20 border-l-4 border-red-400 p-4 rounded-lg backdrop-blur-sm">
      <div class="flex">
        <div class="flex-shrink-0">
          <Icon name="mdi:alert" class="h-5 w-5 text-red-400" />
        </div>
        <div class="ml-3">
          <p class="text-sm text-red-300">{{ error }}</p>
        </div>
      </div>
    </div>

    <!-- No data state -->
    <div v-else-if="!hasActivityData" class="py-8 text-center">
      <Icon name="mdi:chart-timeline-variant" class="mx-auto h-12 w-12 text-gray-400" />
      <h3 class="mt-2 text-sm font-medium text-gray-200">No activity data</h3>
      <p class="mt-1 text-sm text-gray-400">
        There is no activity data available for this user yet.
      </p>
    </div>

    <!-- Statistics content -->
    <div v-else class="space-y-6">
      <!-- Time period selector -->
      <div class="flex justify-end">
        <select
          v-model="selectedTimePeriod"
          class="block w-48 pl-4 pr-10 py-3 text-base border-white/20 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-lg bg-gray-700/50 text-gray-200 backdrop-blur-sm"
        >
          <option value="7">Last 7 days</option>
          <option value="30">Last 30 days</option>
          <option value="90">Last 90 days</option>
          <option value="all">All time</option>
        </select>
      </div>

      <!-- Statistics cards -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <admin-stat-card
          title="Total Logins"
          :value="activityStats.totalLogins"
          icon="mdi:login-variant"
          color="blue"
          :trend="activityStats.loginTrend"
          trend-period="vs previous period"
        />
        <admin-stat-card
          title="Business Cards"
          :value="userContentStats.businessCards"
          icon="mdi:card-account-details"
          color="green"
          :trend="activityStats.contentTrend"
          trend-period="vs previous period"
        />
        <admin-stat-card
          title="Total Content"
          :value="userContentStats.totalContent"
          icon="mdi:folder-multiple"
          color="purple"
          suffix="items"
        />
        <admin-stat-card
          title="Suspicious Activity"
          :value="activityStats.suspiciousRate"
          icon="mdi:alert-circle"
          color="yellow"
          suffix="%"
          :trend="activityStats.suspiciousTrend"
          trend-period="vs previous period"
        />
      </div>

      <!-- Charts section -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Login activity chart -->
        <div class="bg-gradient-to-br from-gray-800/50 via-gray-700/50 to-gray-800/50 backdrop-blur-sm rounded-xl border border-white/10 overflow-hidden">
          <div class="px-6 py-5">
            <h3 class="text-lg leading-6 font-medium text-white">Login Activity</h3>
            <p class="mt-1 max-w-2xl text-sm text-gray-300">Login frequency over time.</p>
          </div>
          <div class="border-t border-white/10 px-6 py-5">
            <div class="h-64">
              <canvas id="loginActivityChart"></canvas>
            </div>
          </div>
        </div>

        <!-- Content creation chart -->
        <div class="bg-gradient-to-br from-gray-800/50 via-gray-700/50 to-gray-800/50 backdrop-blur-sm rounded-xl border border-white/10 overflow-hidden">
          <div class="px-6 py-5">
            <h3 class="text-lg leading-6 font-medium text-white">Content Creation</h3>
            <p class="mt-1 max-w-2xl text-sm text-gray-300">Content created over time.</p>
          </div>
          <div class="border-t border-white/10 px-6 py-5">
            <div class="h-64">
              <canvas id="contentCreationChart"></canvas>
            </div>
          </div>
        </div>
      </div>

      <!-- Activity patterns section -->
      <div class="bg-gradient-to-br from-gray-800/50 via-gray-700/50 to-gray-800/50 backdrop-blur-sm rounded-xl border border-white/10 overflow-hidden">
        <div class="px-6 py-5">
          <h3 class="text-lg leading-6 font-medium text-white">Activity Patterns</h3>
          <p class="mt-1 max-w-2xl text-sm text-gray-300">
            Patterns in user activity by day of week and time of day.
          </p>
        </div>
        <div class="border-t border-white/10 px-6 py-5">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Day of week activity -->
            <div>
              <h4 class="text-sm font-medium text-gray-300 mb-4">Activity by Day of Week</h4>
              <div class="h-48">
                <canvas id="dayOfWeekChart"></canvas>
              </div>
            </div>

            <!-- Time of day activity -->
            <div>
              <h4 class="text-sm font-medium text-gray-300 mb-4">Activity by Time of Day</h4>
              <div class="h-48">
                <canvas id="timeOfDayChart"></canvas>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Suspicious activity summary -->
      <div v-if="suspiciousActivities.length > 0" class="bg-gradient-to-br from-gray-800/50 via-gray-700/50 to-gray-800/50 backdrop-blur-sm rounded-xl border border-white/10 overflow-hidden">
        <div class="px-6 py-5">
          <h3 class="text-lg leading-6 font-medium text-white">Suspicious Activity Summary</h3>
          <p class="mt-1 max-w-2xl text-sm text-gray-300">
            Summary of suspicious activities detected.
          </p>
        </div>
        <div class="border-t border-white/10 px-6 py-5">
          <div class="space-y-4">
            <div v-for="(category, index) in suspiciousActivityCategories" :key="index" class="flex items-center">
              <div class="w-4 h-4 rounded-full mr-3" :class="getCategoryColorClass(category.type)"></div>
              <div class="flex-1">
                <div class="flex justify-between">
                  <span class="text-sm font-medium text-gray-200">{{ category.label }}</span>
                  <span class="text-sm text-gray-400">{{ category.count }} incidents</span>
                </div>
                <div class="w-full bg-gray-600/50 rounded-full h-2.5 mt-2">
                  <div class="h-2.5 rounded-full" :class="getCategoryColorClass(category.type)" :style="{ width: `${category.percentage}%` }"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { Chart, registerables } from 'chart.js'
import moment from 'moment'
import { useUserManagement } from '~/composables/user-management'
import { database } from '~/composables/database'

Chart.register(...registerables)

// Props
const props = defineProps({
  userId: {
    type: String,
    required: true
  },
  activityData: {
    type: Array,
    default: () => []
  }
})

// State
const loading = ref(true)
const error = ref('')
const activityLogs = ref<any[]>([])
const userContentStats = ref({
  businessCards: 0,
  flyers: 0,
  items: 0,
  blogs: 0,
  specials: 0,
  totalContent: 0
})
const selectedTimePeriod = ref('30') // Default to 30 days

// Processed statistics
const activityStats = ref({
  totalLogins: 0,
  loginTrend: 0,
  totalContent: 0,
  contentTrend: 0,
  loginFrequency: 0,
  suspiciousRate: 0,
  suspiciousTrend: 0
})

// Chart references
const loginChart = ref<Chart | null>(null)
const contentChart = ref<Chart | null>(null)
const dayOfWeekChart = ref<Chart | null>(null)
const timeOfDayChart = ref<Chart | null>(null)

// Computed properties
const hasActivityData = computed(() => activityLogs.value.length > 0)

const suspiciousActivities = computed(() => {
  return activityLogs.value.filter(activity => isSuspiciousActivity(activity))
})

const suspiciousActivityCategories = computed(() => {
  const categories: Record<string, { type: string, label: string, count: number, percentage: number }> = {
    'failed_login': { type: 'failed_login', label: 'Failed Login Attempts', count: 0, percentage: 0 },
    'unusual_location': { type: 'unusual_location', label: 'Unusual Locations', count: 0, percentage: 0 },
    'unusual_device': { type: 'unusual_device', label: 'Unusual Devices', count: 0, percentage: 0 },
    'rapid_attempts': { type: 'rapid_attempts', label: 'Rapid Login Attempts', count: 0, percentage: 0 },
    'sensitive_data': { type: 'sensitive_data', label: 'Sensitive Data Access', count: 0, percentage: 0 },
    'unusual_time': { type: 'unusual_time', label: 'Unusual Time of Day', count: 0, percentage: 0 },
    'other': { type: 'other', label: 'Other Suspicious Activity', count: 0, percentage: 0 }
  }
  
  // Count suspicious activities by category
  suspiciousActivities.value.forEach(activity => {
    if (activity.type === 'login' && activity.status === 'failed') {
      categories.failed_login.count++
    } else if (activity.type === 'login' && activity.details?.unusual_location) {
      categories.unusual_location.count++
    } else if (activity.type === 'login' && activity.details?.unusual_device) {
      categories.unusual_device.count++
    } else if (activity.type === 'login' && activity.details?.rapid_attempts) {
      categories.rapid_attempts.count++
    } else if (activity.type === 'activity' && activity.details?.sensitive_data_access) {
      categories.sensitive_data.count++
    } else if (activity.details?.unusual_time) {
      categories.unusual_time.count++
    } else {
      categories.other.count++
    }
  })
  
  // Calculate percentages
  const total = suspiciousActivities.value.length
  Object.keys(categories).forEach(key => {
    categories[key].percentage = total > 0 ? (categories[key].count / total) * 100 : 0
  })
  
  // Return only categories with counts > 0, sorted by count descending
  return Object.values(categories)
    .filter(category => category.count > 0)
    .sort((a, b) => b.count - a.count)
})

// Methods
const { getUserActivityLogs } = useUserManagement()

// Check if an activity is suspicious (copied from user-activity-log.vue for consistency)
const isSuspiciousActivity = (activity: any) => {
  // Failed login attempts
  if (activity.type === 'login' && activity.status === 'failed') {
    return true
  }

  // Unusual login location
  if (activity.type === 'login' && activity.details?.unusual_location) {
    return true
  }

  // Unusual device
  if (activity.type === 'login' && activity.details?.unusual_device) {
    return true
  }

  // Multiple login attempts in short period
  if (activity.type === 'login' && activity.details?.rapid_attempts) {
    return true
  }

  // Sensitive data access
  if (activity.type === 'activity' && activity.details?.sensitive_data_access) {
    return true
  }

  // Unusual time of day
  if (activity.details?.unusual_time) {
    return true
  }

  // Admin flagged activity
  if (activity.flagged_by_admin) {
    return true
  }

  return false
}

// Get color class for suspicious activity category
const getCategoryColorClass = (type: string) => {
  switch (type) {
    case 'failed_login': return 'bg-red-500'
    case 'unusual_location': return 'bg-orange-500'
    case 'unusual_device': return 'bg-yellow-500'
    case 'rapid_attempts': return 'bg-pink-500'
    case 'sensitive_data': return 'bg-purple-500'
    case 'unusual_time': return 'bg-blue-500'
    default: return 'bg-gray-500'
  }
}

// Fetch user content statistics
const fetchUserContentStats = async () => {
  try {
    const { getCollectionWhere } = database()
    const contentTypes = ['businesscards', 'flyers', 'items', 'blogs', 'specials']
    
    const stats = {
      businessCards: 0,
      flyers: 0,
      items: 0,
      blogs: 0,
      specials: 0,
      totalContent: 0
    }
    
    for (const type of contentTypes) {
      try {
        const content = await getCollectionWhere(type, 'created_by', props.userId, '==')
        const count = content ? content.length : 0
        
        if (type === 'businesscards') {
          stats.businessCards = count
        } else {
          stats[type as keyof typeof stats] = count
        }
        
        stats.totalContent += count
      } catch (err) {
        console.error(`Error fetching ${type}:`, err)
      }
    }
    
    userContentStats.value = stats
  } catch (err) {
    console.error('Error fetching user content stats:', err)
  }
}

// Fetch activity data
const fetchActivityData = async () => {
  loading.value = true
  error.value = ''
  
  try {
    // Fetch user content statistics
    await fetchUserContentStats()
    
    // Use pre-loaded data if available, otherwise fetch it
    if (props.activityData && props.activityData.length > 0) {
      activityLogs.value = props.activityData
    } else {
      const logs = await getUserActivityLogs(props.userId)
      activityLogs.value = logs
    }
    
    // Process the data
    processActivityData()
    
  } catch (err: any) {
    error.value = err.message || 'Failed to load activity data'
    console.error('Error fetching activity data:', err)
  } finally {
    loading.value = false
  }
}

// Process activity data into statistics and chart data
const processActivityData = () => {
  if (activityLogs.value.length === 0) return
  
  // Filter activities based on selected time period
  const filteredActivities = filterActivitiesByTimePeriod(activityLogs.value, selectedTimePeriod.value)
  const previousPeriodActivities = getPreviousPeriodActivities(activityLogs.value, selectedTimePeriod.value)
  
  // Calculate basic statistics
  const loginActivities = filteredActivities.filter(a => a.type === 'login')
  const previousLoginActivities = previousPeriodActivities.filter(a => a.type === 'login')
  
  const contentActivities = filteredActivities.filter(a => a.type === 'activity' && a.action?.includes('create'))
  const previousContentActivities = previousPeriodActivities.filter(a => a.type === 'activity' && a.action?.includes('create'))
  
  const suspiciousCount = filteredActivities.filter(a => isSuspiciousActivity(a)).length
  const previousSuspiciousCount = previousPeriodActivities.filter(a => isSuspiciousActivity(a)).length
  
  // Calculate trends (percentage change)
  const loginTrend = calculateTrend(loginActivities.length, previousLoginActivities.length)
  const contentTrend = calculateTrend(contentActivities.length, previousContentActivities.length)
  const suspiciousTrend = calculateTrend(suspiciousCount, previousSuspiciousCount)
  
  // Calculate login frequency (per week)
  const periodInDays = selectedTimePeriod.value === 'all' 
    ? moment().diff(moment(activityLogs.value[activityLogs.value.length - 1].timestamp), 'days') 
    : parseInt(selectedTimePeriod.value)
  
  const loginFrequency = periodInDays > 0 
    ? (loginActivities.length / periodInDays) * 7 
    : 0
  
  // Calculate suspicious activity rate
  const suspiciousRate = filteredActivities.length > 0 
    ? (suspiciousCount / filteredActivities.length) * 100 
    : 0
  
  // Update activity stats
  activityStats.value = {
    totalLogins: loginActivities.length,
    loginTrend,
    totalContent: userContentStats.value.totalContent,
    contentTrend,
    loginFrequency: parseFloat(loginFrequency.toFixed(1)),
    suspiciousRate: parseFloat(suspiciousRate.toFixed(1)),
    suspiciousTrend
  }
  
  // Generate chart data
  generateCharts(filteredActivities)
}

// Filter activities by time period
const filterActivitiesByTimePeriod = (activities: any[], period: string) => {
  if (period === 'all') return activities
  
  const cutoffDate = moment().subtract(parseInt(period), 'days')
  return activities.filter(activity => {
    const activityDate = moment(activity.timestamp)
    return activityDate.isAfter(cutoffDate)
  })
}

// Get activities from the previous period for trend calculation
const getPreviousPeriodActivities = (activities: any[], period: string) => {
  if (period === 'all') return []
  
  const periodDays = parseInt(period)
  const currentPeriodStart = moment().subtract(periodDays, 'days')
  const previousPeriodStart = moment().subtract(periodDays * 2, 'days')
  
  return activities.filter(activity => {
    const activityDate = moment(activity.timestamp)
    return activityDate.isBefore(currentPeriodStart) && activityDate.isAfter(previousPeriodStart)
  })
}

// Calculate trend percentage
const calculateTrend = (current: number, previous: number) => {
  if (previous === 0) return current > 0 ? 100 : 0
  return parseFloat((((current - previous) / previous) * 100).toFixed(1))
}

// Generate charts
const generateCharts = (activities: any[]) => {
  // Destroy existing charts to prevent duplicates
  if (loginChart.value) loginChart.value.destroy()
  if (contentChart.value) contentChart.value.destroy()
  if (dayOfWeekChart.value) dayOfWeekChart.value.destroy()
  if (timeOfDayChart.value) timeOfDayChart.value.destroy()
  
  // Generate login activity chart
  generateLoginActivityChart(activities)
  
  // Generate content creation chart
  generateContentCreationChart(activities)
  
  // Generate day of week chart
  generateDayOfWeekChart(activities)
  
  // Generate time of day chart
  generateTimeOfDayChart(activities)
}

// Generate login activity chart
const generateLoginActivityChart = (activities: any[]) => {
  const loginActivities = activities.filter(a => a.type === 'login')
  if (loginActivities.length === 0) return
  
  // Group by date
  const groupedByDate = groupActivitiesByDate(loginActivities)
  
  // Sort dates
  const sortedDates = Object.keys(groupedByDate).sort((a, b) => 
    moment(a, 'YYYY-MM-DD').diff(moment(b, 'YYYY-MM-DD'))
  )
  
  // Prepare chart data
  const labels = sortedDates.map(date => moment(date, 'YYYY-MM-DD').format('MMM D'))
  const data = sortedDates.map(date => groupedByDate[date].length)
  
  // Create chart
  const ctx = document.getElementById('loginActivityChart') as HTMLCanvasElement
  if (!ctx) return
  
  loginChart.value = new Chart(ctx, {
    type: 'line',
    data: {
      labels,
      datasets: [{
        label: 'Logins',
        data,
        borderColor: '#6366F1',
        backgroundColor: 'rgba(99, 102, 241, 0.1)',
        tension: 0.3,
        fill: true
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          display: false
        }
      },
      scales: {
        x: {
          grid: {
            display: false
          }
        },
        y: {
          beginAtZero: true,
          ticks: {
            precision: 0
          }
        }
      }
    }
  })
}

// Generate content creation chart
const generateContentCreationChart = (activities: any[]) => {
  const contentActivities = activities.filter(a => a.type === 'activity' && a.action?.includes('create'))
  if (contentActivities.length === 0) return
  
  // Group by date
  const groupedByDate = groupActivitiesByDate(contentActivities)
  
  // Sort dates
  const sortedDates = Object.keys(groupedByDate).sort((a, b) => 
    moment(a, 'YYYY-MM-DD').diff(moment(b, 'YYYY-MM-DD'))
  )
  
  // Prepare chart data
  const labels = sortedDates.map(date => moment(date, 'YYYY-MM-DD').format('MMM D'))
  const data = sortedDates.map(date => groupedByDate[date].length)
  
  // Create chart
  const ctx = document.getElementById('contentCreationChart') as HTMLCanvasElement
  if (!ctx) return
  
  contentChart.value = new Chart(ctx, {
    type: 'bar',
    data: {
      labels,
      datasets: [{
        label: 'Content Created',
        data,
        backgroundColor: '#10B981',
        borderColor: '#10B981',
        borderWidth: 1
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          display: false
        }
      },
      scales: {
        x: {
          grid: {
            display: false
          }
        },
        y: {
          beginAtZero: true,
          ticks: {
            precision: 0
          }
        }
      }
    }
  })
}

// Generate day of week chart
const generateDayOfWeekChart = (activities: any[]) => {
  // Count activities by day of week
  const dayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday']
  const dayCounts = Array(7).fill(0)
  
  activities.forEach(activity => {
    const date = moment(activity.timestamp)
    const dayOfWeek = date.day()
    dayCounts[dayOfWeek]++
  })
  
  // Create chart
  const ctx = document.getElementById('dayOfWeekChart') as HTMLCanvasElement
  if (!ctx) return
  
  dayOfWeekChart.value = new Chart(ctx, {
    type: 'bar',
    data: {
      labels: dayNames,
      datasets: [{
        label: 'Activity Count',
        data: dayCounts,
        backgroundColor: '#8B5CF6',
        borderColor: '#8B5CF6',
        borderWidth: 1
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          display: false
        }
      },
      scales: {
        x: {
          grid: {
            display: false
          }
        },
        y: {
          beginAtZero: true,
          ticks: {
            precision: 0
          }
        }
      }
    }
  })
}

// Generate time of day chart
const generateTimeOfDayChart = (activities: any[]) => {
  // Group activities by hour of day
  const hourCounts = Array(24).fill(0)
  
  activities.forEach(activity => {
    const date = moment(activity.timestamp)
    const hour = date.hour()
    hourCounts[hour]++
  })
  
  // Create labels for hours (0-23)
  const hourLabels = Array.from({ length: 24 }, (_, i) => {
    return i === 0 ? '12 AM' : 
           i < 12 ? `${i} AM` : 
           i === 12 ? '12 PM' : 
           `${i - 12} PM`
  })
  
  // Create chart
  const ctx = document.getElementById('timeOfDayChart') as HTMLCanvasElement
  if (!ctx) return
  
  timeOfDayChart.value = new Chart(ctx, {
    type: 'line',
    data: {
      labels: hourLabels,
      datasets: [{
        label: 'Activity Count',
        data: hourCounts,
        borderColor: '#EC4899',
        backgroundColor: 'rgba(236, 72, 153, 0.1)',
        tension: 0.3,
        fill: true
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          display: false
        }
      },
      scales: {
        x: {
          grid: {
            display: false
          },
          ticks: {
            maxRotation: 45,
            minRotation: 45
          }
        },
        y: {
          beginAtZero: true,
          ticks: {
            precision: 0
          }
        }
      }
    }
  })
}

// Helper function to group activities by date
const groupActivitiesByDate = (activities: any[]) => {
  const grouped: Record<string, any[]> = {}
  
  activities.forEach(activity => {
    const date = moment(activity.timestamp).format('YYYY-MM-DD')
    if (!grouped[date]) {
      grouped[date] = []
    }
    grouped[date].push(activity)
  })
  
  return grouped
}

// Watch for time period changes
watch(selectedTimePeriod, () => {
  processActivityData()
})

// Fetch data on component mount
onMounted(() => {
  fetchActivityData()
})
</script>
