<template>
  <div>
    <div v-if="loading" class="py-8 flex justify-center">
      <div class="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-400"></div>
    </div>

    <div v-else-if="error" class="bg-red-500/20 border-l-4 border-red-400 p-4 rounded-lg backdrop-blur-sm">
      <div class="flex">
        <div class="flex-shrink-0">
          <Icon name="mdi:alert" class="h-5 w-5 text-red-400" />
        </div>
        <div class="ml-3">
          <p class="text-sm text-red-300">{{ error }}</p>
        </div>
      </div>
    </div>

    <div v-else>
      <!-- Activity Filtering -->
      <div class="py-6 px-6 flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div class="flex items-center gap-4">
          <h3 class="text-lg font-medium text-white">Activity History</h3>
          <button
            @click="generateSampleLogs"
            class="text-xs px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200"
            v-if="activityLogs.length === 0"
          >
            Generate Sample Data
          </button>
        </div>

        <div class="flex  gap-3">
          <select
            v-model="activityTypeFilter"
            class="block w-full max-w-xs pl-4 pr-10 py-3 text-base border-white/20 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-lg bg-gray-700/50 text-gray-200 backdrop-blur-sm"
          >
            <option value="all">All Activity Types</option>
            <option value="login">Login History</option>
            <option value="activity">User Actions</option>
            <option value="admin_action">Admin Actions</option>
            <option value="suspicious">Suspicious Activity</option>
          </select>

          <select
            v-model="timeRangeFilter"
            class="block w-full max-w-xs pl-4 pr-10 py-3 text-base border-white/20 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-lg bg-gray-700/50 text-gray-200 backdrop-blur-sm"
          >
            <option value="all">All Time</option>
            <option value="24h">Last 24 Hours</option>
            <option value="7d">Last 7 Days</option>
            <option value="30d">Last 30 Days</option>
          </select>
        </div>
      </div>

      <!-- Suspicious Activity Summary -->
      <div v-if="suspiciousActivities.length > 0" class="mx-6 mb-6">
        <div class="bg-yellow-500/20 border-l-4 border-yellow-400 p-6 rounded-lg backdrop-blur-sm">
          <div class="flex">
            <div class="flex-shrink-0">
              <Icon name="mdi:alert-circle" class="h-5 w-5 text-yellow-400" />
            </div>
            <div class="ml-3">
              <h3 class="text-sm font-medium text-yellow-300">
                {{ suspiciousActivities.length }} suspicious {{ suspiciousActivities.length === 1 ? 'activity' : 'activities' }} detected
              </h3>
              <div class="mt-2 text-sm text-yellow-200">
                <p>
                  Unusual patterns detected in user activity. Review the flagged items below.
                </p>
              </div>
              <div class="mt-4">
                <div class="flex flex-wrap gap-2">
                  <button
                    @click="activityTypeFilter = 'suspicious'"
                    class="bg-yellow-500/30 px-4 py-2 rounded-lg text-sm font-medium text-yellow-200 hover:bg-yellow-500/40 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500 transition-all duration-200 border border-yellow-400/20"
                  >
                    View suspicious activities
                  </button>
                  <button
                    @click="markAllAsReviewed"
                    class="bg-yellow-500/30 px-4 py-2 rounded-lg text-sm font-medium text-yellow-200 hover:bg-yellow-500/40 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500 transition-all duration-200 border border-yellow-400/20"
                  >
                    Mark all as reviewed
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="border-t border-white/10">
        <!-- Activity Timeline -->
        <ul class="divide-y divide-white/10">
          <li v-for="(activity, index) in filteredActivityLogs" :key="index" class="px-6 py-6 hover:bg-gray-800/30 transition-colors duration-200">
            <div class="flex space-x-4">
              <!-- Activity Icon -->
              <div class="flex-shrink-0">
                <div
                  class="h-10 w-10 rounded-full flex items-center justify-center border"
                  :class="getActivityTypeClass(activity.type, isSuspiciousActivity(activity))"
                >
                  <Icon :name="getActivityIcon(activity, isSuspiciousActivity(activity))" class="h-5 w-5" />
                </div>
              </div>

              <!-- Activity Content -->
              <div class="flex-1 space-y-2">
                <div class="flex items-center justify-between">
                  <h3 class="text-sm font-medium flex items-center" :class="getActivityTextClass(activity.type)">
                    {{ getActivityTitle(activity) }}
                    <span
                      v-if="isSuspiciousActivity(activity)"
                      class="ml-2 inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-yellow-500/20 text-yellow-300 border border-yellow-400/20"
                    >
                      <Icon name="mdi:alert-circle" class="h-3 w-3 mr-1" />
                      Suspicious
                    </span>
                    <span
                      v-if="activity.reviewed"
                      class="ml-2 inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-green-500/20 text-green-300 border border-green-400/20"
                    >
                      <Icon name="mdi:check-circle" class="h-3 w-3 mr-1" />
                      Reviewed
                    </span>
                  </h3>
                  <p class="text-sm text-gray-400">{{ formatActivityDate(activity.timestamp) }}</p>
                </div>
                <p class="text-sm text-gray-300">{{ getActivityDescription(activity) }}</p>

                <!-- Suspicious activity reason -->
                <p v-if="isSuspiciousActivity(activity) && !activity.reviewed" class="text-sm text-yellow-300 mt-2 p-3 bg-yellow-500/20 border border-yellow-400/20 rounded-lg">
                  <Icon name="mdi:alert-circle-outline" class="h-4 w-4 inline-block mr-2" />
                  {{ getSuspiciousReason(activity) }}
                </p>

                <!-- Additional details for certain activity types -->
                <div v-if="activity.details && Object.keys(activity.details).length > 0" class="mt-3">
                  <button
                    @click="toggleDetails(index)"
                    class="flex items-center text-sm text-blue-400 hover:text-blue-300 transition-colors duration-200"
                  >
                    <Icon
                      :name="expandedDetails.includes(index) ? 'mdi:chevron-down' : 'mdi:chevron-right'"
                      class="h-5 w-5 mr-1"
                    />
                    {{ expandedDetails.includes(index) ? 'Hide details' : 'View details' }}
                  </button>

                  <div v-if="expandedDetails.includes(index)" class="mt-3 bg-gray-800/50 p-4 rounded-lg border border-white/10">
                    <pre class="text-xs text-gray-300 overflow-auto">{{ JSON.stringify(activity.details, null, 2) }}</pre>
                  </div>
                </div>

                <!-- Mark as reviewed button for suspicious activities -->
                <div v-if="isSuspiciousActivity(activity) && !activity.reviewed" class="mt-3">
                  <button
                    @click="markAsReviewed(activity)"
                    class="inline-flex items-center px-4 py-2 border border-transparent text-xs font-medium rounded-lg shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200"
                  >
                    <Icon name="mdi:check" class="h-3 w-3 mr-2" />
                    Mark as reviewed
                  </button>
                </div>
              </div>
            </div>
          </li>

          <li v-if="filteredActivityLogs.length === 0" class="px-6 py-12 text-center">
            <Icon name="mdi:calendar-blank" class="mx-auto h-12 w-12 text-gray-400" />
            <h3 class="mt-2 text-sm font-medium text-gray-200">No activity found</h3>
            <p class="mt-1 text-sm text-gray-400">
              There is no activity matching your current filters.
            </p>
            <div class="mt-6">
              <button
                @click="resetFilters"
                class="inline-flex items-center px-6 py-3 border border-transparent shadow-sm text-sm font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200"
              >
                Reset Filters
              </button>
            </div>
          </li>
        </ul>

        <!-- Load More Button -->
        <div v-if="hasMoreLogs" class="px-6 py-6 flex justify-center border-t border-white/10">
          <button
            @click="loadMoreLogs"
            :disabled="loadingMore"
            class="inline-flex items-center px-6 py-3 border border-white/20 shadow-sm text-sm font-medium rounded-lg text-gray-300 bg-gray-700/50 hover:bg-gray-600/50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200 backdrop-blur-sm disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <Icon name="mdi:loading" class="h-4 w-4 mr-2 animate-spin" v-if="loadingMore" />
            Load More
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import moment from 'moment'
import { useUserManagement } from '~/composables/user-management'
import { useActivityLogger } from '~/composables/activity-logger'

// Props
const props = defineProps({
  userId: {
    type: String,
    required: true
  },
  limit: {
    type: Number,
    default: 20
  }
})

// Emits
const emit = defineEmits(['suspicious-activity-detected', 'activity-reviewed'])

// State
const activityLogs = ref<any[]>([])
const loading = ref(true)
const loadingMore = ref(false)
const error = ref('')
const page = ref(1)
const hasMoreLogs = ref(false)
const expandedDetails = ref<number[]>([])
const activityTypeFilter = ref('all')
const timeRangeFilter = ref('all')

const { getUserActivityLogs, logAdminAction } = useUserManagement()
const { createSampleLogs } = useActivityLogger()

// Suspicious activity detection
const isSuspiciousActivity = (activity: any) => {
  // Failed login attempts
  if (activity.type === 'login' && activity.status === 'failed') {
    return true
  }

  // Unusual login location
  if (activity.type === 'login' && activity.details?.unusual_location) {
    return true
  }

  // Unusual device
  if (activity.type === 'login' && activity.details?.unusual_device) {
    return true
  }

  // Multiple login attempts in short period
  if (activity.type === 'login' && activity.details?.rapid_attempts) {
    return true
  }

  // Sensitive data access
  if (activity.type === 'activity' && activity.details?.sensitive_data_access) {
    return true
  }

  // Unusual time of day
  if (activity.details?.unusual_time) {
    return true
  }

  // Admin flagged activity
  if (activity.flagged_by_admin) {
    return true
  }

  return false
}

// Get reason for suspicious activity
const getSuspiciousReason = (activity: any) => {
  if (activity.type === 'login' && activity.status === 'failed') {
    return 'Failed login attempt'
  }

  if (activity.type === 'login' && activity.details?.unusual_location) {
    return `Login from unusual location: ${activity.details.location || 'Unknown'}`
  }

  if (activity.type === 'login' && activity.details?.unusual_device) {
    return `Login from unusual device: ${activity.details.device || 'Unknown'}`
  }

  if (activity.type === 'login' && activity.details?.rapid_attempts) {
    return 'Multiple login attempts in short period'
  }

  if (activity.type === 'activity' && activity.details?.sensitive_data_access) {
    return 'Access to sensitive data'
  }

  if (activity.details?.unusual_time) {
    return 'Activity at unusual time'
  }

  if (activity.flagged_by_admin) {
    return `Flagged by admin: ${activity.flagged_reason || 'No reason provided'}`
  }

  return 'Unusual activity pattern detected'
}

// Mark activity as reviewed
const markAsReviewed = async (activity: any) => {
  try {
    // Update the activity in the database
    // This would typically involve a call to update the activity record
    // For now, we'll just update it locally
    activity.reviewed = true
    activity.reviewed_at = new Date()
    activity.reviewed_by = 'current_admin' // This would be the current admin user

    // Log the admin action
    await logAdminAction('review_suspicious_activity', props.userId, {
      activity_id: activity.id,
      activity_type: activity.type,
      activity_timestamp: activity.timestamp
    })

    // Emit event
    emit('activity-reviewed', activity)

  } catch (err) {
    console.error('Error marking activity as reviewed:', err)
    error.value = 'Failed to mark activity as reviewed'
  }
}

// Mark all suspicious activities as reviewed
const markAllAsReviewed = async () => {
  try {
    // Update all suspicious activities
    for (const activity of suspiciousActivities.value) {
      activity.reviewed = true
      activity.reviewed_at = new Date()
      activity.reviewed_by = 'current_admin' // This would be the current admin user
    }

    // Log the admin action
    await logAdminAction('review_all_suspicious_activities', props.userId, {
      count: suspiciousActivities.value.length
    })

    // Emit event
    emit('activity-reviewed', suspiciousActivities.value)

  } catch (err) {
    console.error('Error marking all activities as reviewed:', err)
    error.value = 'Failed to mark activities as reviewed'
  }
}

// Computed properties
const suspiciousActivities = computed(() => {
  return activityLogs.value.filter(activity => isSuspiciousActivity(activity) && !activity.reviewed)
})

// Fetch user activity logs
const fetchActivityLogs = async (reset = false) => {
  if (reset) {
    page.value = 1
    activityLogs.value = []
  }

  const isInitialLoad = activityLogs.value.length === 0

  if (isInitialLoad) {
    loading.value = true
  } else {
    loadingMore.value = true
  }

  try {
    const logs = await getUserActivityLogs(props.userId)

    // Calculate start and end indices based on page and limit
    const start = (page.value - 1) * props.limit
    const end = start + props.limit

    // Get the current page of logs
    const newLogs = logs.slice(start, end)

    // Append logs if loading more, otherwise replace
    if (isInitialLoad || reset) {
      activityLogs.value = newLogs
    } else {
      activityLogs.value = [...activityLogs.value, ...newLogs]
    }

    // Check if there are more logs
    hasMoreLogs.value = logs.length > end

    // Check for suspicious activities
    if (suspiciousActivities.value.length > 0) {
      emit('suspicious-activity-detected', suspiciousActivities.value)
    }

  } catch (err: any) {
    error.value = err.message || 'Failed to load activity logs'
  } finally {
    loading.value = false
    loadingMore.value = false
  }
}

// Load more logs
const loadMoreLogs = async () => {
  if (loadingMore.value) return

  page.value++
  await fetchActivityLogs(false)
}

// Toggle details expansion
const toggleDetails = (index: number) => {
  const expandedIndex = expandedDetails.value.indexOf(index)

  if (expandedIndex === -1) {
    expandedDetails.value.push(index)
  } else {
    expandedDetails.value.splice(expandedIndex, 1)
  }
}

// Reset filters
const resetFilters = () => {
  activityTypeFilter.value = 'all'
  timeRangeFilter.value = 'all'
}

// Filtered activity logs
const filteredActivityLogs = computed(() => {
  let filtered = [...activityLogs.value]

  // Filter by activity type
  if (activityTypeFilter.value === 'suspicious') {
    filtered = filtered.filter(log => isSuspiciousActivity(log))
  } else if (activityTypeFilter.value !== 'all') {
    filtered = filtered.filter(log => log.type === activityTypeFilter.value)
  }

  // Filter by time range
  if (timeRangeFilter.value !== 'all') {
    const now = moment()
    let cutoffDate

    switch (timeRangeFilter.value) {
      case '24h':
        cutoffDate = now.clone().subtract(24, 'hours')
        break
      case '7d':
        cutoffDate = now.clone().subtract(7, 'days')
        break
      case '30d':
        cutoffDate = now.clone().subtract(30, 'days')
        break
    }

    if (cutoffDate) {
      filtered = filtered.filter(log => {
        const logDate = log.timestamp instanceof Date
          ? moment(log.timestamp)
          : moment(log.timestamp)
        return logDate.isAfter(cutoffDate)
      })
    }
  }

  return filtered
})

// Generate sample activity logs for testing
const generateSampleLogs = async () => {
  try {
    await createSampleLogs(props.userId)
    // Refresh the activity logs
    await fetchActivityLogs(true)
  } catch (err: any) {
    console.error('Error generating sample logs:', err)
    error.value = 'Failed to generate sample logs'
  }
}

// Activity formatting helpers
const formatActivityDate = (timestamp: any) => {
  if (!timestamp) return ''

  const date = timestamp instanceof Date ? timestamp : new Date(timestamp)
  return moment(date).fromNow()
}

const getActivityTypeClass = (type: string, isSuspicious = false) => {
  if (isSuspicious) {
    return 'bg-yellow-500/20 border-yellow-400/20'
  }

  switch (type) {
    case 'login':
      return 'bg-green-500/20 border-green-400/20'
    case 'activity':
      return 'bg-blue-500/20 border-blue-400/20'
    case 'admin_action':
      return 'bg-purple-500/20 border-purple-400/20'
    default:
      return 'bg-gray-500/20 border-gray-400/20'
  }
}

const getActivityTextClass = (type: string, isSuspicious = false) => {
  if (isSuspicious) {
    return 'text-yellow-300'
  }

  switch (type) {
    case 'login':
      return 'text-green-300'
    case 'activity':
      return 'text-blue-300'
    case 'admin_action':
      return 'text-purple-300'
    default:
      return 'text-gray-300'
  }
}

const getActivityIcon = (activity: any, isSuspicious = false) => {
  if (isSuspicious) {
    return 'mdi:alert-circle'
  }

  if (activity.type === 'login') {
    return 'mdi:login'
  }

  if (activity.type === 'admin_action') {
    if (activity.action === 'update_profile') return 'mdi:account-edit'
    if (activity.action === 'update_role') return 'mdi:shield-account'
    if (activity.action === 'toggle_status') return 'mdi:account-switch'
    return 'mdi:shield'
  }

  // Default activity icons based on action type
  return 'mdi:history'
}

const getActivityTitle = (activity: any) => {
  if (activity.type === 'login') {
    return 'User Login'
  }

  if (activity.type === 'admin_action') {
    switch (activity.action) {
      case 'update_profile':
        return 'Profile Updated by Admin'
      case 'update_role':
        return 'Role Updated'
      case 'toggle_status':
        return activity.details?.makeActive
          ? 'Account Activated'
          : 'Account Deactivated'
      default:
        return `Admin Action: ${activity.action || 'Unknown'}`
    }
  }

  if (activity.type === 'activity') {
    return activity.title || 'User Activity'
  }

  return 'System Event'
}

const getActivityDescription = (activity: any) => {
  if (activity.type === 'login') {
    const device = activity.device || 'Unknown device'
    const ip = activity.ip_address || 'Unknown location'
    return `Logged in from ${device} at ${ip}`
  }

  if (activity.type === 'admin_action') {
    const admin = activity.performedByEmail || 'an administrator'
    switch (activity.action) {
      case 'update_profile':
        const fields = activity.details?.fields || []
        return `Profile information updated by ${admin}${fields.length ? `: ${fields.join(', ')}` : ''}`
      case 'update_role':
        return `User role updated by ${admin}`
      case 'toggle_status':
        return activity.details?.makeActive
          ? `Account activated by ${admin}`
          : `Account deactivated by ${admin}`
      default:
        return activity.description || `Action performed by ${admin}`
    }
  }

  if (activity.type === 'activity') {
    return activity.description || 'User performed an action'
  }

  return activity.description || 'System event occurred'
}

// Watch for filter changes to refresh data
watch([activityTypeFilter, timeRangeFilter], () => {
  fetchActivityLogs(true)
}, { deep: true })

// Fetch data on component mount
onMounted(() => {
  fetchActivityLogs()
})
</script>
