<template>
  <div>
    <div v-if="loading" class="py-8 flex justify-center">
      <div class="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-400"></div>
    </div>

    <div v-else-if="error" class="bg-red-500/20 border-l-4 border-red-400 p-4 rounded-lg backdrop-blur-sm">
      <div class="flex">
        <div class="flex-shrink-0">
          <Icon name="mdi:alert" class="h-5 w-5 text-red-400" />
        </div>
        <div class="ml-3">
          <p class="text-sm text-red-300">{{ error }}</p>
        </div>
      </div>
    </div>

    <div v-else>
      <!-- Content Filtering -->
      <div class="py-6 px-6 flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <h3 class="text-lg font-medium text-white">User Content</h3>

        <div class="flex flex-wrap gap-3">
          <select
            v-model="contentTypeFilter"
            class="block w-full max-w-xs pl-4 pr-10 py-3 text-base border-white/20 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-lg bg-gray-700/50 text-gray-200 backdrop-blur-sm"
          >
            <option value="all">All Content Types</option>
            <option value="businesscards">Business Cards</option>
            <option value="flyers">Flyers</option>
            <option value="items">Items</option>
            <option value="blogs">Blog Posts</option>
            <option value="specials">Special Offers</option>
          </select>

          <select
            v-model="statusFilter"
            class="block w-full max-w-xs pl-4 pr-10 py-3 text-base border-white/20 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-lg bg-gray-700/50 text-gray-200 backdrop-blur-sm"
          >
            <option value="all">All Status</option>
            <option value="approved">Approved</option>
            <option value="pending">Pending</option>
            <option value="rejected">Rejected</option>
          </select>
        </div>
      </div>
      
      <div class="border-t border-white/10">
        <!-- Content Grid -->
        <div v-if="contentItems.length > 0" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 p-6">
          <div
            v-for="item in filteredContentItems"
            :key="item.id"
            class="bg-gradient-to-br from-gray-800/50 via-gray-700/50 to-gray-800/50 backdrop-blur-sm border border-white/10 rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-105"
          >
            <!-- Content Preview -->
            <div class="h-40 bg-gray-700/50 relative overflow-hidden">
              <img
                v-if="item.image && item.image.src"
                :src="item.image.src"
                class="w-full h-full object-cover"
                alt="Content preview"
              />
              <div v-else class="w-full h-full flex items-center justify-center">
                <Icon :name="getContentTypeIcon(item.type)" class="h-12 w-12 text-gray-400" />
              </div>

              <!-- Status Badge -->
              <div
                class="absolute top-3 right-3 px-3 py-1 text-xs font-medium rounded-full border backdrop-blur-sm"
                :class="getStatusClasses(item.status)"
              >
                {{ item.status }}
              </div>
            </div>

            <div class="p-5">
              <!-- Content Info -->
              <div class="flex items-center text-xs text-gray-400 mb-3">
                <span class="capitalize">{{ item.type }}</span>
                <span class="mx-2">•</span>
                <span>{{ formatDate(item.created_at || item.createdAt) }}</span>
              </div>

              <h3 class="text-sm font-medium text-gray-200 mb-2 truncate">
                {{ item.title || item.name || 'Untitled' }}
              </h3>

              <p v-if="item.description" class="text-xs text-gray-400 line-clamp-2 mb-4">
                {{ item.description }}
              </p>

              <!-- View Details Button -->
              <div class="mt-3 flex justify-end">
                <NuxtLink
                  :to="getContentUrl(item)"
                  class="text-xs text-blue-400 hover:text-blue-300 transition-colors duration-200 flex items-center"
                >
                  View Details
                  <Icon name="mdi:arrow-right" class="h-3 w-3 ml-1" />
                </NuxtLink>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Empty State -->
        <div v-else class="px-6 py-12 text-center">
          <Icon name="mdi:file-document-outline" class="mx-auto h-12 w-12 text-gray-400" />
          <h3 class="mt-2 text-sm font-medium text-gray-200">No content found</h3>
          <p class="mt-1 text-sm text-gray-400">
            {{ noContentMessage }}
          </p>
          <div v-if="hasFiltersApplied" class="mt-6">
            <button
              @click="resetFilters"
              class="inline-flex items-center px-6 py-3 border border-transparent shadow-sm text-sm font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200"
            >
              Reset Filters
            </button>
          </div>
        </div>

        <!-- Load More Button -->
        <div v-if="hasMoreContent" class="px-6 py-6 flex justify-center border-t border-white/10">
          <button
            @click="loadMore"
            :disabled="loadingMore"
            class="inline-flex items-center px-6 py-3 border border-white/20 shadow-sm text-sm font-medium rounded-lg text-gray-300 bg-gray-700/50 hover:bg-gray-600/50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200 backdrop-blur-sm disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <Icon name="mdi:loading" class="h-4 w-4 mr-2 animate-spin" v-if="loadingMore" />
            Load More
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import moment from 'moment'

// Props
const props = defineProps({
  userId: {
    type: String,
    required: true
  },
  pageSize: {
    type: Number,
    default: 12
  }
})

// State
const contentItems = ref<any[]>([])
const loading = ref(true)
const loadingMore = ref(false)
const error = ref('')
const page = ref(1)
const hasMoreContent = ref(false)
const contentTypeFilter = ref('all')
const statusFilter = ref('all')

// Fetch user content
const fetchUserContent = async (reset = false) => {
  if (reset) {
    page.value = 1
    contentItems.value = []
  }
  
  const isInitialLoad = contentItems.value.length === 0
  
  if (isInitialLoad) {
    loading.value = true
  } else {
    loadingMore.value = true
  }
  
  try {
    const contentTypes = ['businesscards', 'flyers', 'items', 'blogs', 'specials']
    const newItems = []
    
    const { getCollection } = database()
    
    // For each content type, fetch user's content
    for (const type of contentTypes) {
      try {
        const { getCollectionWhere } = database()
        const items = await getCollectionWhere(type, 'user_id', props.userId, '==')
        
        if (items && Array.isArray(items)) {
          // Add type info to each item
          const typedItems = items.map(item => ({ ...item, type }))
          newItems.push(...typedItems)
        }
      } catch (err) {
        console.error(`Error fetching ${type}:`, err)
      }
    }
    
    // Sort by creation date (newest first)
    newItems.sort((a, b) => {
      const dateA = a.created_at || a.createdAt || new Date(0)
      const dateB = b.created_at || b.createdAt || new Date(0)
      return new Date(dateB).getTime() - new Date(dateA).getTime()
    })
    
    // Calculate start and end indices based on page and pageSize
    const start = (page.value - 1) * props.pageSize
    const end = start + props.pageSize
    
    // Get the current page of content
    const paginatedItems = newItems.slice(0, end)
    
    // Update state
    contentItems.value = paginatedItems
    hasMoreContent.value = newItems.length > end
    
  } catch (err: any) {
    error.value = err.message || 'Failed to load user content'
  } finally {
    loading.value = false
    loadingMore.value = false
  }
}

// Load more content
const loadMore = async () => {
  if (loadingMore.value) return
  
  page.value++
  await fetchUserContent(false)
}

// Reset filters
const resetFilters = () => {
  contentTypeFilter.value = 'all'
  statusFilter.value = 'all'
}

// Computed properties
const filteredContentItems = computed(() => {
  let filtered = [...contentItems.value]
  
  // Filter by content type
  if (contentTypeFilter.value !== 'all') {
    filtered = filtered.filter(item => item.type === contentTypeFilter.value)
  }
  
  // Filter by status
  if (statusFilter.value !== 'all') {
    filtered = filtered.filter(item => item.status === statusFilter.value)
  }
  
  return filtered
})

const hasFiltersApplied = computed(() => {
  return contentTypeFilter.value !== 'all' || statusFilter.value !== 'all'
})

const noContentMessage = computed(() => {
  if (hasFiltersApplied.value) {
    return 'No content matches your current filter criteria.'
  }
  return 'This user has not created any content yet.'
})

// Helper functions
const formatDate = (date: any) => {
  if (!date) return 'Unknown date'
  return moment(date).format('MMM D, YYYY')
}

const getContentTypeIcon = (type: string) => {
  switch (type) {
    case 'businesscards':
      return 'mdi:card-account-details'
    case 'flyers':
      return 'mdi:flyer'
    case 'items':
      return 'mdi:shopping'
    case 'blogs':
      return 'mdi:post'
    case 'specials':
      return 'mdi:tag-multiple'
    default:
      return 'mdi:file-document'
  }
}

const getStatusClasses = (status: string) => {
  switch (status) {
    case 'approved':
      return 'bg-green-500/20 text-green-300 border-green-400/20'
    case 'pending':
      return 'bg-yellow-500/20 text-yellow-300 border-yellow-400/20'
    case 'rejected':
      return 'bg-red-500/20 text-red-300 border-red-400/20'
    default:
      return 'bg-gray-500/20 text-gray-300 border-gray-400/20'
  }
}

const getContentUrl = (item: any) => {
  switch (item.type) {
    case 'businesscards':
      return `/c/businesscards/${item.id}`
    case 'flyers':
      return `/c/flyers/${item.id}`
    case 'items':
      return `/c/items/${item.id}`
    case 'blogs':
      return `/c/blogs/${item.id}`
    case 'specials':
      return `/c/specials/${item.id}`
    default:
      return '#'
  }
}

// Watch for filter changes to refresh data
watch([contentTypeFilter, statusFilter], () => {
  // No need to fetch again, just filter the existing data
}, { deep: true })

// Fetch data on component mount
onMounted(() => {
  fetchUserContent()
})
</script>
