<template>
  <div class="bg-gradient-to-br from-gray-800/50 via-gray-700/50 to-gray-800/50 backdrop-blur-sm rounded-xl border border-white/10 overflow-hidden">
    <div class="px-6 py-5 bg-blue-600/20 border-b border-white/10">
      <h2 class="text-lg font-semibold text-white">User Role Management</h2>
    </div>

    <div class="p-6">
      <div v-if="loading" class="flex justify-center py-8">
        <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-400"></div>
      </div>

      <div v-else-if="error" class="text-center py-8">
        <div class="bg-red-500/20 border border-red-400/20 rounded-lg p-6">
          <p class="text-red-300 mb-4">{{ error }}</p>
          <button
            @click="fetchUsers"
            class="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200"
          >
            Try Again
          </button>
        </div>
      </div>
      
      <div v-else>
        <!-- Search and Filter -->
        <div class="mb-6 flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <div class="relative flex-1">
            <input
              type="text"
              v-model="searchQuery"
              placeholder="Search users..."
              class="w-full px-4 py-3 border border-white/20 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-gray-700/50 text-gray-200 placeholder-gray-400 backdrop-blur-sm"
            />
            <button
              v-if="searchQuery"
              @click="searchQuery = ''"
              class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-300 transition-colors duration-200"
            >
              <Icon name="mdi:close" />
            </button>
          </div>

          <div class="flex gap-3">
            <select
              v-model="roleFilter"
              class="px-4 py-3 border border-white/20 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-gray-700/50 text-gray-200 backdrop-blur-sm"
            >
              <option value="all">All Roles</option>
              <option value="Admin">Admin</option>
              <option value="User">User</option>
            </select>

            <select
              v-model="statusFilter"
              class="px-4 py-3 border border-white/20 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-gray-700/50 text-gray-200 backdrop-blur-sm"
            >
              <option value="all">All Status</option>
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
            </select>
          </div>
        </div>
        
        <!-- Users Table -->
        <div class="overflow-x-auto rounded-lg border border-white/20 bg-gray-800/30 backdrop-blur-sm">
          <table class="min-w-full divide-y divide-white/10">
            <thead class="bg-gray-800/50">
              <tr>
                <th scope="col" class="px-6 py-4 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                  User
                </th>
                <th scope="col" class="px-6 py-4 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                  Email
                </th>
                <th scope="col" class="px-6 py-4 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                  Roles
                </th>
                <th scope="col" class="px-6 py-4 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                  Status
                </th>
                <th scope="col" class="px-6 py-4 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody class="bg-gray-800/20 divide-y divide-white/10">
              <tr v-for="user in filteredUsers" :key="user.id" class="hover:bg-gray-700/30 transition-colors duration-200">
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="flex items-center">
                    <div class="flex-shrink-0 h-10 w-10 rounded-full bg-gray-600/50 flex items-center justify-center overflow-hidden border border-white/20">
                      <img v-if="user.avatar && user.avatar.src" :src="user.avatar.src" alt="User avatar" class="h-full w-full object-cover" />
                      <span v-else class="text-gray-300 font-medium">{{ user.first_name ? user.first_name.charAt(0) : 'U' }}</span>
                    </div>
                    <div class="ml-4">
                      <NuxtLink :to="`/c/user/${user.id}`" class="text-sm font-medium text-gray-200 hover:text-blue-400 transition-colors duration-200">
                        {{ user.first_name }} {{ user.last_name }}
                      </NuxtLink>
                      <div class="text-sm text-gray-400">{{ user.user_name || 'No username' }}</div>
                    </div>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-300">{{ user.email }}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="flex flex-wrap gap-1">
                    <span
                      v-for="(role, index) in user.roles"
                      :key="index"
                      :class="[
                        'px-2 py-1 text-xs font-semibold rounded-full border',
                        role === 'Admin' ? 'bg-purple-500/20 text-purple-300 border-purple-400/20' : 'bg-blue-500/20 text-blue-300 border-blue-400/20'
                      ]"
                    >
                      {{ role }}
                    </span>
                    <span v-if="!user.roles || user.roles.length === 0" class="text-sm text-gray-400">No roles</span>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span :class="[
                    'px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full border',
                    user.active !== false ? 'bg-green-500/20 text-green-300 border-green-400/20' : 'bg-red-500/20 text-red-300 border-red-400/20'
                  ]">
                    {{ user.active !== false ? 'Active' : 'Inactive' }}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <div class="flex space-x-2">
                    <button
                      @click="toggleUserRole(user)"
                      :class="[
                        'px-3 py-2 rounded-lg text-xs font-medium transition-all duration-200 border',
                        isUserAdmin(user) ? 'bg-yellow-500/20 text-yellow-300 border-yellow-400/20 hover:bg-yellow-500/30' : 'bg-purple-500/20 text-purple-300 border-purple-400/20 hover:bg-purple-500/30'
                      ]"
                    >
                      {{ isUserAdmin(user) ? 'Remove Admin' : 'Make Admin' }}
                    </button>
                    <button
                      @click="toggleUserStatus(user)"
                      :class="[
                        'px-3 py-2 rounded-lg text-xs font-medium transition-all duration-200 border',
                        user.active !== false ? 'bg-red-500/20 text-red-300 border-red-400/20 hover:bg-red-500/30' : 'bg-green-500/20 text-green-300 border-green-400/20 hover:bg-green-500/30'
                      ]"
                    >
                      {{ user.active !== false ? 'Deactivate' : 'Activate' }}
                    </button>
                  </div>
                </td>
              </tr>
              <tr v-if="filteredUsers.length === 0">
                <td colspan="5" class="px-6 py-8 text-center text-gray-400">
                  No users found matching your criteria
                </td>
              </tr>
            </tbody>
          </table>
        </div>
        
        <!-- Pagination -->
        <div class="mt-6 flex items-center justify-between">
          <div class="text-sm text-gray-300">
            Showing <span class="font-medium text-white">{{ filteredUsers.length }}</span> of <span class="font-medium text-white">{{ users.length }}</span> users
          </div>
          <div class="flex space-x-2">
            <button
              @click="currentPage = Math.max(1, currentPage - 1)"
              :disabled="currentPage === 1"
              class="px-4 py-2 border border-white/20 rounded-lg text-sm font-medium text-gray-300 bg-gray-700/50 hover:bg-gray-600/50 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 backdrop-blur-sm"
            >
              Previous
            </button>
            <button
              @click="currentPage = Math.min(Math.ceil(filteredUsers.length / itemsPerPage), currentPage + 1)"
              :disabled="currentPage >= Math.ceil(filteredUsers.length / itemsPerPage)"
              class="px-4 py-2 border border-white/20 rounded-lg text-sm font-medium text-gray-300 bg-gray-700/50 hover:bg-gray-600/50 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 backdrop-blur-sm"
            >
              Next
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'

// State variables
const users = ref([])
const loading = ref(true)
const error = ref('')
const searchQuery = ref('')
const roleFilter = ref('all')
const statusFilter = ref('all')
const currentPage = ref(1)
const itemsPerPage = ref(10)

// Fetch users from database
const fetchUsers = async () => {
  loading.value = true
  error.value = ''
  
  try {
    const { getCollection } = database()
    const usersData = await getCollection('user')
    
    if (usersData) {
      users.value = usersData.map((user: any) => ({
        ...user,
        roles: user.roles || ['User']
      }))
    } else {
      error.value = 'No users found'
    }
  } catch (err) {
    console.error('Error fetching users:', err)
    error.value = 'Failed to load users'
  } finally {
    loading.value = false
  }
}

// Check if a user is an admin
const isUserAdmin = (user: any) => {
  return user.roles && user.roles.includes('Admin')
}

// Toggle user admin role
const toggleUserRole = async (user: any) => {
  try {
    const isAdmin = isUserAdmin(user)
    const { updateUserRole } = database()
    const result = await updateUserRole(user.id, !isAdmin)
    
    if (result.success) {
      // Update local state
      const userIndex = users.value.findIndex((u: any) => u.id === user.id)
      if (userIndex !== -1) {
        users.value[userIndex].roles = result.roles
      }
      alert(`User role updated successfully`)
    } else {
      console.error('Error updating user role:', result.error)
      alert('Failed to update user role. Please try again.')
    }
  } catch (err) {
    console.error('Error toggling user role:', err)
    alert('Failed to update user role. Please try again.')
  }
}

// Toggle user status
const toggleUserStatus = async (user: any) => {
  try {
    const isActive = user.active !== false
    const { toggleUserStatus } = database()
    const result = await toggleUserStatus(user.id, !isActive)
    
    if (result.success) {
      // Update local state
      const userIndex = users.value.findIndex((u: any) => u.id === user.id)
      if (userIndex !== -1) {
        users.value[userIndex].active = !isActive
      }
      alert(`User is now ${!isActive ? 'active' : 'inactive'}`)
    } else {
      console.error('Error updating user status:', result.error)
      alert('Failed to update user status. Please try again.')
    }
  } catch (err) {
    console.error('Error toggling user status:', err)
    alert('Failed to update user status. Please try again.')
  }
}

// Filter users based on search query and filters
const filteredUsers = computed(() => {
  let result = [...users.value]
  
  // Apply search filter
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    result = result.filter((user: any) => {
      const fullName = `${user.first_name || ''} ${user.last_name || ''}`.toLowerCase()
      const email = (user.email || '').toLowerCase()
      const username = (user.user_name || '').toLowerCase()
      
      return fullName.includes(query) || email.includes(query) || username.includes(query)
    })
  }
  
  // Apply role filter
  if (roleFilter.value !== 'all') {
    result = result.filter((user: any) => {
      return user.roles && user.roles.includes(roleFilter.value)
    })
  }
  
  // Apply status filter
  if (statusFilter.value !== 'all') {
    result = result.filter((user: any) => {
      return statusFilter.value === 'active' ? user.active !== false : user.active === false
    })
  }
  
  return result
})

// Reset page when filters change
watch([searchQuery, roleFilter, statusFilter], () => {
  currentPage.value = 1
})

// Fetch users on component mount
onMounted(() => {
  fetchUsers()
})
</script>
